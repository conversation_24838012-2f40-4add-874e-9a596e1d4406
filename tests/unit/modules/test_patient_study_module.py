"""
Test PatientStudyModule functionality.

PatientStudyModule implements DICOM PS3.3 C.7.2.2 Patient Study Module.
Contains attributes that provide information about the Patient at the time the Study started.
"""

import pydicom
from pyrt_dicom.modules import PatientStudyModule
from pyrt_dicom.enums.study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered
from pyrt_dicom.validators import ValidationResult


class TestPatientStudyModule:
    """Test PatientStudyModule functionality."""

    def test_from_required_elements_success(self):
        """Test successful creation with required elements (none - all are Type 2C/3)."""
        patient_study = PatientStudyModule.from_required_elements()

        # Module should be created successfully with no required elements
        assert patient_study is not None
        assert isinstance(patient_study, PatientStudyModule)

        # Test dataset generation
        dataset = patient_study.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)

    def test_with_optional_elements_validation(self):
        """Test that with_optional_elements accepts arguments but validation catches misuse."""
        patient_study = PatientStudyModule.from_required_elements()

        # Should work with no arguments
        result = patient_study.with_optional_elements()
        assert result is patient_study

        # Should accept arguments without raising exception (validation will catch misuse)
        result = patient_study.with_optional_elements(patients_age="045Y")
        assert result is patient_study
        
        # Note: Misuse of with_optional_elements should be caught by validation, not exceptions
        # Users should use specialized methods like with_patient_demographics() instead
    
    def test_with_patient_demographics(self):
        """Test adding patient demographic information."""
        patient_study = PatientStudyModule.from_required_elements()

        # Test basic demographics
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_size=1.75,
            patients_weight=70.5,
            smoking_status=SmokingStatus.NO
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "045Y"
        assert dataset.PatientSize == 1.75
        assert dataset.PatientWeight == 70.5
        assert dataset.SmokingStatus == "NO"
        assert patient_study.has_demographic_info

    def test_with_medical_information(self):
        """Test adding medical information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_medical_information(
            medical_alerts="Drug allergies: Penicillin",
            allergies="Penicillin, Shellfish",
            additional_patient_history="Previous surgery in 2020"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.MedicalAlerts == "Drug allergies: Penicillin"
        assert dataset.Allergies == "Penicillin, Shellfish"
        assert dataset.AdditionalPatientHistory == "Previous surgery in 2020"
        assert patient_study.has_medical_info

    def test_with_pregnancy_information(self):
        """Test adding pregnancy information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_pregnancy_information(
            pregnancy_status=PregnancyStatus.NOT_PREGNANT,
            last_menstrual_date="20240101"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PregnancyStatus == 1  # NOT_PREGNANT value
        assert dataset.LastMenstrualDate == "20240101"
        assert patient_study.has_pregnancy_info

    def test_with_visit_information(self):
        """Test adding visit information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_visit_information(
            admission_id="ADM001",
            reason_for_visit="Routine checkup",
            service_episode_id="EP001"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.AdmissionID == "ADM001"
        assert dataset.ReasonForVisit == "Routine checkup"
        assert dataset.ServiceEpisodeID == "EP001"
        assert patient_study.has_visit_info
    
    def test_with_non_human_organism_info(self):
        """Test adding non-human organism information."""
        patient_study = PatientStudyModule.from_required_elements()

        patient_study.with_non_human_organism_info(
            patients_sex_neutered=PatientSexNeutered.ALTERED
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientSexNeutered == "ALTERED"
        assert patient_study.is_non_human_organism

    def test_smoking_status_enum_values(self):
        """Test all smoking status enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        smoking_values = [SmokingStatus.YES, SmokingStatus.NO, SmokingStatus.UNKNOWN]

        for smoking_status in smoking_values:
            patient_study.with_patient_demographics(smoking_status=smoking_status)
            dataset = patient_study.to_dataset()
            assert dataset.SmokingStatus == smoking_status.value

    def test_pregnancy_status_enum_values(self):
        """Test all pregnancy status enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        pregnancy_values = [
            PregnancyStatus.NOT_PREGNANT,
            PregnancyStatus.POSSIBLY_PREGNANT,
            PregnancyStatus.DEFINITELY_PREGNANT,
            PregnancyStatus.UNKNOWN
        ]

        for pregnancy_status in pregnancy_values:
            patient_study.with_pregnancy_information(pregnancy_status=pregnancy_status)
            dataset = patient_study.to_dataset()
            assert dataset.PregnancyStatus == pregnancy_status.value
    
    def test_patient_sex_neutered_enum_values(self):
        """Test all patient sex neutered enum values."""
        patient_study = PatientStudyModule.from_required_elements()

        sex_neutered_values = [PatientSexNeutered.ALTERED, PatientSexNeutered.UNALTERED]

        for sex_neutered in sex_neutered_values:
            patient_study.with_non_human_organism_info(patients_sex_neutered=sex_neutered)
            dataset = patient_study.to_dataset()
            assert dataset.PatientSexNeutered == sex_neutered.value

    def test_property_helpers(self):
        """Test property helper methods."""
        patient_study = PatientStudyModule.from_required_elements()

        # Initially no information
        assert not patient_study.has_demographic_info
        assert not patient_study.has_medical_info
        assert not patient_study.has_pregnancy_info
        assert not patient_study.has_visit_info
        assert not patient_study.is_non_human_organism
        assert not patient_study.has_gender_identity_info
        assert not patient_study.has_sex_parameters_for_clinical_use
        assert not patient_study.has_person_names_to_use
        assert not patient_study.has_third_person_pronouns

        # Add demographic info
        patient_study.with_patient_demographics(patients_age="030Y")
        assert patient_study.has_demographic_info

        # Add medical info
        patient_study.with_medical_information(allergies="None known")
        assert patient_study.has_medical_info

        # Add pregnancy info
        patient_study.with_pregnancy_information(pregnancy_status=PregnancyStatus.UNKNOWN)
        assert patient_study.has_pregnancy_info

        # Add visit info
        patient_study.with_visit_information(admission_id="TEST001")
        assert patient_study.has_visit_info

        # Add non-human organism info
        patient_study.with_non_human_organism_info(patients_sex_neutered=PatientSexNeutered.UNALTERED)
        assert patient_study.is_non_human_organism
    
    def test_method_chaining(self):
        """Test that methods can be chained together."""
        patient_study = PatientStudyModule.from_required_elements().with_patient_demographics(
            patients_age="035Y",
            patients_weight=65.0
        ).with_medical_information(
            allergies="None known"
        ).with_visit_information(
            reason_for_visit="Annual exam"
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "035Y"
        assert dataset.PatientWeight == 65.0
        assert dataset.Allergies == "None known"
        assert dataset.ReasonForVisit == "Annual exam"

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        patient_study = PatientStudyModule.from_required_elements()

        assert hasattr(patient_study, 'validate')
        assert callable(patient_study.validate)

        # Test validation result structure
        validation_result = patient_study.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_public_validation_methods_exist(self):
        """Test that all public validation methods exist and are callable."""
        patient_study = PatientStudyModule.from_required_elements()

        # Test all public validation methods exist
        validation_methods = [
            'check_required_elements',
            'check_conditional_requirements', 
            'check_enum_constraints',
            'check_sequence_requirements'
        ]
        
        for method_name in validation_methods:
            assert hasattr(patient_study, method_name), f"Missing method: {method_name}"
            assert callable(getattr(patient_study, method_name)), f"Method not callable: {method_name}"
            
            # Test each method returns ValidationResult
            result = getattr(patient_study, method_name)()
            assert isinstance(result, ValidationResult), f"Method {method_name} does not return ValidationResult"
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')

    def test_private_validation_methods_exist(self):
        """Test that all private validation methods exist and are callable."""
        patient_study = PatientStudyModule.from_required_elements()

        # Test all private validation methods exist
        private_methods = [
            '_ensure_required_elements_valid',
            '_ensure_conditional_requirements_valid',
            '_ensure_enum_constraints_valid', 
            '_ensure_sequence_requirements_valid'
        ]
        
        for method_name in private_methods:
            assert hasattr(patient_study, method_name), f"Missing private method: {method_name}"
            assert callable(getattr(patient_study, method_name)), f"Private method not callable: {method_name}"

    def test_zero_copy_validation_with_self_reference(self):
        """Test that validation methods use zero-copy by passing self instead of to_dataset()."""
        patient_study = PatientStudyModule.from_required_elements()
        patient_study.with_patient_demographics(
            patients_age="045Y",
            smoking_status=SmokingStatus.NO
        )

        # Test public validation methods with zero-copy
        result = patient_study.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass since no required elements in Patient Study Module

        result = patient_study.check_conditional_requirements() 
        assert isinstance(result, ValidationResult)

        result = patient_study.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass with valid enum

        result = patient_study.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass with no sequences

        # Test main validate method with zero-copy
        result = patient_study.validate()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0  # Should pass overall

    def test_private_validation_methods_raise_exceptions(self):
        """Test that private validation methods raise ValidationError on failure."""
        patient_study = PatientStudyModule.from_required_elements()
        patient_study.with_patient_demographics(
            smoking_status="INVALID_STATUS"  # Invalid enum value
        )

        # Test that private methods raise ValidationError on validation failure
        # Note: Required elements should not raise since there are none in Patient Study Module
        try:
            patient_study._ensure_required_elements_valid()
            # Should pass since no required elements
        except Exception as e:
            assert False, f"_ensure_required_elements_valid should not raise exception: {e}"

        # Test enum validation with invalid value
        try:
            patient_study._ensure_enum_constraints_valid()
            # This might pass since the validator uses warnings for invalid enum values
        except Exception as e:
            # If it raises, it should be ValidationError
            from pyrt_dicom.validators import ValidationError
            assert isinstance(e, ValidationError), f"Expected ValidationError, got {type(e)}"

    def test_conditional_requirements_validation(self):
        """Test Type 2C conditional requirements validation.""" 
        patient_study = PatientStudyModule.from_required_elements()
        
        # Test non-human organism conditional requirement
        patient_study.PatientSpeciesDescription = "Canine"
        # Missing PatientSexNeutered should produce error
        result = patient_study.check_conditional_requirements()
        assert len(result.errors) > 0
        assert any("Patient's Sex Neutered" in error and "non-human organism" in error for error in result.errors)
        
        # Add required field
        patient_study.with_non_human_organism_info(PatientSexNeutered.ALTERED)
        result = patient_study.check_conditional_requirements()
        # Should not have the conditional requirement error anymore
        conditional_errors = [e for e in result.errors if "non-human organism" in e]
        assert len(conditional_errors) == 0
        
    def test_sequence_structure_validation(self):
        """Test sequence structure validation."""
        patient_study = PatientStudyModule.from_required_elements()
        
        # Create invalid gender identity sequence (missing required code sequence)
        from pydicom import Dataset
        gender_item = Dataset()
        # Missing GenderIdentityCodeSequence (Type 1 within sequence)
        patient_study.with_gender_identity([gender_item])
        
        result = patient_study.check_sequence_requirements()
        assert len(result.errors) > 0
        assert any("Gender Identity Code Sequence" in error and "Type 1" in error for error in result.errors)
        
        # Fix the sequence
        code_item = Dataset()
        code_item.CodeValue = "F"
        code_item.CodeMeaning = "Female"
        gender_item.GenderIdentityCodeSequence = [code_item]
        patient_study.with_gender_identity([gender_item])
        
        result = patient_study.check_sequence_requirements()
        sequence_errors = [e for e in result.errors if "Gender Identity Code Sequence" in e]
        assert len(sequence_errors) == 0

    def test_enum_constraints_validation(self):
        """Test enumerated value constraints validation."""
        patient_study = PatientStudyModule.from_required_elements()
        
        # Test valid enum values
        patient_study.with_patient_demographics(smoking_status=SmokingStatus.YES)
        result = patient_study.check_enum_constraints()
        assert len(result.errors) == 0
        
        # Test invalid enum value
        patient_study.SmokingStatus = "INVALID_VALUE"
        result = patient_study.check_enum_constraints()
        # Should produce warning for invalid enum
        assert len(result.warnings) > 0
        assert any("Smoking Status" in warning for warning in result.warnings)

    def test_optional_none_values_ignored(self):
        """Test that None values are ignored in optional methods."""
        patient_study = PatientStudyModule.from_required_elements()

        # None values should not set attributes
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_size=None,  # Should be ignored
            patients_weight=70.0
        )

        # Test via dataset generation
        dataset = patient_study.to_dataset()
        assert dataset.PatientAge == "045Y"
        assert not hasattr(dataset, 'PatientSize')
        assert dataset.PatientWeight == 70.0
    
    def test_create_sequence_items(self):
        """Test static methods for creating sequence items."""
        # Test gender identity item creation
        gender_identity_item = PatientStudyModule.create_gender_identity_item(
            gender_identity_code_sequence=[
                PatientStudyModule.create_gender_identity_code(
                    code_meaning="Female",
                    code_value="F",
                )
            ],
            gender_identity_comment="Test comment"
        )

        assert hasattr(gender_identity_item, 'GenderIdentityCodeSequence')
        assert hasattr(gender_identity_item, 'GenderIdentityComment')
        assert gender_identity_item.GenderIdentityComment == "Test comment"

        # Test person name item creation
        name_item = PatientStudyModule.create_person_name_to_use_item(
            name_to_use="Jane Doe",
            name_to_use_comment="Preferred name"
        )

        assert hasattr(name_item, 'NameToUse')
        assert hasattr(name_item, 'NameToUseComment')
        assert name_item.NameToUse == "Jane Doe"
        assert name_item.NameToUseComment == "Preferred name"

    def test_dataset_generation(self):
        """Test that to_dataset() generates proper pydicom Dataset."""
        patient_study = PatientStudyModule.from_required_elements()

        # Add some data
        patient_study.with_patient_demographics(
            patients_age="045Y",
            patients_weight=70.5
        ).with_medical_information(
            allergies="None known"
        )

        # Generate dataset
        dataset = patient_study.to_dataset()

        # Verify dataset type and content
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) == 3  # Should have 3 elements
        assert dataset.PatientAge == "045Y"
        assert dataset.PatientWeight == 70.5
        assert dataset.Allergies == "None known"

        # Verify dataset is a copy (not reference)
        dataset.PatientAge = "050Y"
        dataset2 = patient_study.to_dataset()
        assert dataset2.PatientAge == "045Y"  # Should still be original value

    def test_create_gender_identity_code_with_required_parameters(self):
        """Test creating gender identity code with required parameters."""
        code_item = PatientStudyModule.create_gender_identity_code(
            code_meaning="Female"
        )

        # Verify dataset type and required attribute
        assert isinstance(code_item, pydicom.Dataset)
        assert hasattr(code_item, 'CodeMeaning')
        assert code_item.CodeMeaning == "Female"

        # Verify optional attributes are present but empty
        assert hasattr(code_item, 'CodeValue')
        assert hasattr(code_item, 'LongCodeValue')
        assert hasattr(code_item, 'CodingSchemeDesignator')
        assert hasattr(code_item, 'CodingSchemeVersion')
        assert hasattr(code_item, 'UrnCodeValue')
        assert code_item.CodeValue == ""
        assert code_item.LongCodeValue == ""
        assert code_item.CodingSchemeDesignator == ""
        assert code_item.CodingSchemeVersion == ""
        assert code_item.UrnCodeValue == ""

    def test_create_gender_identity_code_with_all_parameters(self):
        """Test creating gender identity code with all parameters."""
        code_item = PatientStudyModule.create_gender_identity_code(
            code_meaning="Female",
            code_value="F",
            long_code_value="Female",
            coding_scheme_designator="CID",
            coding_scheme_version="1.0",
            urn_code_value="urn:oid:1.2.3.4.5"
        )

        # Verify all attributes are set correctly
        assert isinstance(code_item, pydicom.Dataset)
        assert code_item.CodeMeaning == "Female"
        assert code_item.CodeValue == "F"
        assert code_item.LongCodeValue == "Female"
        assert code_item.CodingSchemeDesignator == "CID"
        assert code_item.CodingSchemeVersion == "1.0"
        assert code_item.UrnCodeValue == "urn:oid:1.2.3.4.5"

    def test_create_gender_identity_code_with_empty_meaning(self):
        """Test creating gender identity code with empty code_meaning (technically allowed)."""
        code_item = PatientStudyModule.create_gender_identity_code(code_meaning="")
        
        # Verify dataset is created with empty code_meaning
        assert isinstance(code_item, pydicom.Dataset)
        assert hasattr(code_item, 'CodeMeaning')
        assert code_item.CodeMeaning == ""

    def test_create_gender_identity_code_usage_in_sequence(self):
        """Test using create_gender_identity_code in a gender identity sequence."""
        # Create gender identity item with code sequence
        gender_identity_item = PatientStudyModule.create_gender_identity_item(
            gender_identity_code_sequence=[
                PatientStudyModule.create_gender_identity_code(
                    code_meaning="Female",
                    code_value="F"
                )
            ],
            gender_identity_comment="Patient identifies as female"
        )

        # Verify the structure
        assert hasattr(gender_identity_item, 'GenderIdentityCodeSequence')
        assert hasattr(gender_identity_item, 'GenderIdentityComment')
        assert len(gender_identity_item.GenderIdentityCodeSequence) == 1
        assert gender_identity_item.GenderIdentityCodeSequence[0].CodeMeaning == "Female"
        assert gender_identity_item.GenderIdentityCodeSequence[0].CodeValue == "F"
        assert gender_identity_item.GenderIdentityComment == "Patient identifies as female"

"""
Test ImagePlaneModule (C - Conditional) functionality.

ImagePlaneModule implements DICOM PS3.3 C.7.6.2 Image Plane Module with
composition-based architecture and comprehensive geometric validation.
Required when GeneralImageModule is present for spatial positioning.
"""

import pytest
import pydicom
from pyrt_dicom.modules.image_plane_module import ImagePlaneModule
from pyrt_dicom.validators import ValidationResult, ValidationError


class TestImagePlaneModule:
    """Test ImagePlaneModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements using composition architecture."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        # Test composition pattern - access via to_dataset()
        dataset = plane.to_dataset()
        assert dataset.PixelSpacing == [1.0, 1.0]
        assert dataset.ImageOrientationPatient == [1, 0, 0, 0, 1, 0]
        assert dataset.ImagePositionPatient == [0.0, 0.0, 0.0]
        assert dataset.SliceThickness == 3.0
        
        # Test geometric validation properties
        assert plane.is_orthogonal
        assert plane.is_normalized
        assert plane.is_geometrically_valid
    
    def test_pixel_spacing_validation(self):
        """Test pixel spacing validation for dose grids."""
        # Common RT dose pixel spacings
        pixel_spacings = [
            [1.0, 1.0],         # 1mm isotropic
            [2.0, 2.0],         # 2mm isotropic
            [2.5, 2.5],         # 2.5mm isotropic
            [1.0, 2.0],         # Anisotropic
            [0.5, 0.5],         # High resolution
            [5.0, 5.0]          # Low resolution
        ]
        
        for spacing in pixel_spacings:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=spacing,
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=3.0
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.PixelSpacing == spacing
            
            # Test pixel area calculation
            expected_area = spacing[0] * spacing[1]
            assert plane.pixel_area == expected_area
    
    def test_image_orientation_patient_validation(self):
        """Test image orientation patient validation."""
        # Standard anatomical orientations
        orientations = [
            [1, 0, 0, 0, 1, 0],     # Axial (standard)
            [0, 1, 0, 0, 0, -1],    # Sagittal
            [1, 0, 0, 0, 0, -1],    # Coronal
            [-1, 0, 0, 0, 1, 0],    # Axial flipped
            [0, -1, 0, 0, 0, -1]    # Sagittal flipped
        ]
        
        for orientation in orientations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=orientation,
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=3.0
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.ImageOrientationPatient == orientation
            
            # Test geometric properties
            assert plane.is_orthogonal  # All test orientations are orthogonal
            assert plane.is_normalized  # All test orientations are normalized
    
    def test_image_position_patient_validation(self):
        """Test image position patient validation."""
        # Various coordinate positions for dose grids
        positions = [
            [0.0, 0.0, 0.0],           # Origin
            [-100.0, -100.0, -50.0],   # Typical dose grid corner
            [150.0, 200.0, -100.0],    # Extended field
            [-200.0, -150.0, 100.0],   # Different quadrant
            [0.5, -0.5, 25.5]          # Sub-millimeter precision
        ]
        
        for position in positions:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=position,
                slice_thickness=3.0
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.ImagePositionPatient == position
    
    def test_slice_thickness_validation(self):
        """Test slice thickness validation for dose grids."""
        # Common RT dose slice thicknesses
        thicknesses = [1.0, 2.0, 2.5, 3.0, 5.0, 10.0]
        
        for thickness in thicknesses:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=thickness
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.SliceThickness == thickness
            
            # Test voxel volume calculation
            expected_volume = 2.0 * 2.0 * thickness  # pixel_area * thickness
            assert plane.voxel_volume == expected_volume
    
    def test_with_optional_elements(self):
        """Test adding optional plane elements."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        ).with_optional_elements(
            slice_location=0.0,
            spacing_between_slices=3.0,
        )
        
        # Test composition pattern - access via to_dataset()
        dataset = plane.to_dataset()
        assert hasattr(dataset, 'SliceLocation')
        assert hasattr(dataset, 'SpacingBetweenSlices')
        assert dataset.SliceLocation == 0.0
        assert dataset.SpacingBetweenSlices == 3.0
        
        # Test method chaining
        assert isinstance(plane, ImagePlaneModule)
    
    def test_slice_location_progression(self):
        """Test slice location progression for multi-slice dose."""
        slice_locations = [-15.0, -12.0, -9.0, -6.0, -3.0, 0.0, 3.0, 6.0, 9.0]
        
        for location in slice_locations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, location],
                slice_thickness=3.0
            ).with_optional_elements(
                slice_location=location
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.SliceLocation == location
    
    def test_spacing_between_slices(self):
        """Test spacing between slices validation."""
        # Spacing should match or exceed slice thickness
        spacing_values = [2.5, 3.0, 3.5, 5.0, 10.0]
        
        for spacing in spacing_values:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.5
            ).with_optional_elements(
                spacing_between_slices=spacing
            )
            # Test composition pattern - access via to_dataset()
            dataset = plane.to_dataset()
            assert dataset.SpacingBetweenSlices == spacing
            
            # Test spacing validation property
            assert plane.has_valid_spacing_between_slices
    
    def test_rt_dose_grid_specifications(self):
        """Test RT dose grid spatial specifications."""
        # Typical RT dose grid setup
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.5, 2.5],  # 2.5mm resolution
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Axial
            image_position_patient=[-128.75, -128.75, -100.0],  # Grid corner
            slice_thickness=2.5
        ).with_optional_elements(
            slice_location=-100.0,
            spacing_between_slices=2.5
        )
        
        # Verify dose grid characteristics using composition pattern
        dataset = plane.to_dataset()
        assert dataset.PixelSpacing == [2.5, 2.5]
        assert dataset.SliceThickness == 2.5
        assert dataset.SpacingBetweenSlices == 2.5  # Contiguous slices
        
        # Test geometric validation
        assert plane.is_geometrically_valid
    
    def test_anisotropic_voxel_spacing(self):
        """Test anisotropic voxel spacing configurations."""
        # Non-isotropic voxel configurations
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 2.0],  # Different x,y spacing
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=5.0  # Different z spacing
        )
        
        # Test composition pattern - access via to_dataset()
        dataset = plane.to_dataset()
        assert dataset.PixelSpacing[0] != dataset.PixelSpacing[1]
        assert dataset.SliceThickness != dataset.PixelSpacing[0]
        
        # Test voxel volume with anisotropic spacing
        expected_volume = 1.0 * 2.0 * 5.0  # Different x,y,z spacings
        assert plane.voxel_volume == expected_volume
    
    def test_coordinate_system_consistency(self):
        """Test coordinate system consistency for dose alignment."""
        # Ensure right-handed coordinate system
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Standard axial
            image_position_patient=[-100.0, -100.0, 0.0],
            slice_thickness=3.0
        )
        
        # Verify coordinate system properties using composition pattern
        dataset = plane.to_dataset()
        orientation = dataset.ImageOrientationPatient
        assert len(orientation) == 6  # Two 3D vectors
        
        # Row direction (first 3 elements)
        row_direction = orientation[:3]
        assert len(row_direction) == 3
        
        # Column direction (last 3 elements)
        col_direction = orientation[3:]
        assert len(col_direction) == 3
        
        # Test right-handed coordinate system
        assert plane.is_right_handed_coordinate_system
    
    def test_high_resolution_dose_grid(self):
        """Test high resolution dose grid specifications."""
        # High-resolution dose calculation
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.5, 0.5],  # Sub-millimeter resolution
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[-64.25, -64.25, -25.0],
            slice_thickness=1.0
        ).with_optional_elements(
            spacing_between_slices=1.0
        )
        
        # Verify high-resolution characteristics using composition pattern
        dataset = plane.to_dataset()
        assert all(spacing < 1.0 for spacing in dataset.PixelSpacing)
        assert dataset.SliceThickness == 1.0
    
    def test_dependency_on_general_image_module(self):
        """Test that ImagePlaneModule depends on GeneralImageModule."""
        # This dependency should be validated at IOD level
        # Here we test that ImagePlaneModule can be created independently
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        # Module should be valid on its own using composition pattern
        dataset = plane.to_dataset()
        assert dataset.PixelSpacing == [2.0, 2.0]
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        assert hasattr(plane, 'validate')
        assert callable(plane.validate)
        
        # Test validation result structure
        validation_result = plane.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_granular_validation_methods_exist(self):
        """Test that all granular validation methods exist and are callable."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        # Test public validation methods exist
        validation_methods = [
            'check_required_elements',
            'check_conditional_requirements', 
            'check_enum_constraints',
            'check_sequence_requirements',
            'check_geometric_constraints',
            'check_coordinate_system'
        ]
        
        for method_name in validation_methods:
            assert hasattr(plane, method_name), f"Missing method: {method_name}"
            assert callable(getattr(plane, method_name)), f"Method not callable: {method_name}"
            
            # Test that method returns ValidationResult
            result = getattr(plane, method_name)()
            assert isinstance(result, ValidationResult), f"Method {method_name} should return ValidationResult"
        
        # Test private validation methods exist
        private_methods = [
            '_ensure_required_elements_valid',
            '_ensure_conditional_requirements_valid',
            '_ensure_enum_constraints_valid', 
            '_ensure_sequence_requirements_valid',
            '_ensure_geometric_constraints_valid',
            '_ensure_coordinate_system_valid'
        ]
        
        for method_name in private_methods:
            assert hasattr(plane, method_name), f"Missing private method: {method_name}"
            assert callable(getattr(plane, method_name)), f"Private method not callable: {method_name}"


class TestImagePlaneModuleGeometricValidation:
    """Test comprehensive geometric validation features."""
    
    def test_dataset_generation_method(self):
        """Test that to_dataset() generates proper pydicom Dataset."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        # Test dataset generation
        dataset = plane.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) == 4  # Should have 4 attributes
        
        # Test independence (changes to returned dataset don't affect module)
        dataset.PixelSpacing = [999.0, 999.0]
        fresh_dataset = plane.to_dataset()
        assert fresh_dataset.PixelSpacing == [1.0, 1.0]
    
    def test_orthogonality_validation_success(self):
        """Test orthogonality validation with valid orthogonal vectors."""
        orthogonal_orientations = [
            [1, 0, 0, 0, 1, 0],     # Standard axial
            [0, 1, 0, 0, 0, -1],    # Sagittal
            [1, 0, 0, 0, 0, -1],    # Coronal
            [-1, 0, 0, 0, -1, 0],   # Flipped axial
        ]
        
        for orientation in orthogonal_orientations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[1.0, 1.0],
                image_orientation_patient=orientation,
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.0
            )
            assert plane.is_orthogonal, f"Failed for orientation {orientation}"
            assert plane.is_normalized, f"Failed normalization for {orientation}"
            assert plane.is_geometrically_valid
    
    def test_orthogonality_validation_via_validator(self):
        """Test that non-orthogonal vectors are detected by validator, not module creation."""
        # Module should create successfully without validation
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 1, 0, 0, 1, 0],  # Not orthogonal
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Module should still return False for is_orthogonal
        assert not plane.is_orthogonal
        assert not plane.is_geometrically_valid
        
        # Validation should catch the error
        result = plane.validate()
        assert result.has_errors
        assert any("orthogonal" in error for error in result.errors)
    
    def test_normalization_validation_via_validator(self):
        """Test that non-normalized vectors are detected by validator, not module creation."""
        # Module should create successfully without validation
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[2, 0, 0, 0, 2, 0],  # Not normalized
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Module should still return False for is_normalized
        assert not plane.is_normalized
        assert not plane.is_geometrically_valid
        
        # Validation should catch the error
        result = plane.validate()
        assert result.has_errors
        assert any("normalized" in error for error in result.errors)
    
    def test_right_handed_coordinate_system(self):
        """Test right-handed coordinate system validation."""
        # Standard right-handed orientations
        right_handed_orientations = [
            [1, 0, 0, 0, 1, 0],     # Standard axial (right-handed)
            [0, 1, 0, 0, 0, -1],    # Sagittal (right-handed)
            [1, 0, 0, 0, 0, -1],    # Coronal (right-handed)
        ]
        
        for orientation in right_handed_orientations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[1.0, 1.0],
                image_orientation_patient=orientation,
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.0
            )
            assert plane.is_right_handed_coordinate_system, f"Failed for {orientation}"
    
    def test_pixel_coordinate_mapping(self):
        """Test real-world coordinate mapping using DICOM equation C.7.6.2.1-1."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Standard axial
            image_position_patient=[-50.0, -50.0, 10.0],   # Corner position
            slice_thickness=2.0
        )
        
        # Test coordinate mapping for various pixel positions
        test_cases = [
            (0, 0, [-50.0, -50.0, 10.0]),  # Upper left corner
            (10, 0, [-50.0, -40.0, 10.0]), # 10 pixels down
            (0, 10, [-40.0, -50.0, 10.0]), # 10 pixels right
            (5, 5, [-45.0, -45.0, 10.0]),  # Center-ish
        ]
        
        for row, col, expected_coords in test_cases:
            coords = plane.get_pixel_coordinates(row, col)
            assert coords is not None, f"Failed to calculate coords for ({row}, {col})"
            assert len(coords) == 3
            for i, (actual, expected) in enumerate(zip(coords, expected_coords)):
                assert abs(actual - expected) < 1e-6, f"Coordinate {i} mismatch: {actual} vs {expected}"
    
    def test_spacing_between_slices_validation(self):
        """Test spacing between slices validation logic."""
        # Test valid positive spacing
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        ).with_optional_elements(spacing_between_slices=2.5)
        
        assert plane.has_valid_spacing_between_slices
        
        # Test that negative spacing doesn't raise error from module
        plane_negative = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        ).with_optional_elements(spacing_between_slices=-1.0)
        
        # Module should allow negative values but property should return False
        assert not plane_negative.has_valid_spacing_between_slices
        
        # Validation should catch the error
        result = plane_negative.validate()
        assert result.has_errors
        assert any("must not be negative" in error for error in result.errors)


class TestImagePlaneModuleValidationIntegration:
    """Test validation integration with zero-copy optimization and ValidationError handling."""
    
    def test_check_required_elements_success(self):
        """Test successful validation of required elements with zero-copy."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_check_required_elements_failure(self):
        """Test validation failure for missing required elements."""
        plane = ImagePlaneModule()
        # Don't set any required elements
        
        result = plane.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert len(result.errors) > 0
        # Should have errors for missing PixelSpacing, ImageOrientationPatient, ImagePositionPatient
        error_text = ' '.join(result.errors)
        assert 'Pixel Spacing' in error_text or 'PixelSpacing' in error_text
        assert 'Image Orientation' in error_text or 'ImageOrientationPatient' in error_text  
        assert 'Image Position' in error_text or 'ImagePositionPatient' in error_text
    
    def test_check_conditional_requirements_success(self):
        """Test successful validation of conditional requirements."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_check_conditional_requirements_failure(self):
        """Test validation failure for missing conditional requirements."""
        plane = ImagePlaneModule()
        plane.PixelSpacing = [1.0, 1.0]
        plane.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        plane.ImagePositionPatient = [0.0, 0.0, 0.0]
        # Missing SliceThickness (Type 2)
        
        result = plane.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any('Slice Thickness' in error or 'SliceThickness' in error for error in result.errors)
    
    def test_check_geometric_constraints_success(self):
        """Test successful geometric constraint validation."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Orthogonal unit vectors
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_geometric_constraints()
        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_check_geometric_constraints_failure(self):
        """Test geometric constraint validation failure."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 1, 0, 0, 1, 0],  # Not orthogonal
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_geometric_constraints()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any('orthogonal' in error for error in result.errors)
    
    def test_check_coordinate_system_success(self):
        """Test successful coordinate system validation."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Right-handed system
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_coordinate_system()
        assert isinstance(result, ValidationResult)
        # Should either have no errors or only warnings about orthonormal basis
        right_handed_errors = [error for error in result.errors if 'right-handed' in error]
        assert len(right_handed_errors) == 0
    
    def test_check_coordinate_system_failure(self):
        """Test coordinate system validation failure for left-handed system."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, -1, 0],  # Left-handed system
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result = plane.check_coordinate_system()
        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any('right-handed' in error for error in result.errors)
    
    def test_private_ensure_methods_success(self):
        """Test private ensure methods with valid data don't raise exceptions."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # These should not raise exceptions with valid data
        plane._ensure_required_elements_valid()
        plane._ensure_conditional_requirements_valid()
        plane._ensure_enum_constraints_valid()
        plane._ensure_sequence_requirements_valid()
        plane._ensure_geometric_constraints_valid()
        plane._ensure_coordinate_system_valid()
    
    def test_private_ensure_methods_raise_validation_error(self):
        """Test private ensure methods raise ValidationError with invalid data."""
        plane = ImagePlaneModule()
        # No required elements set
        
        # Should raise ValidationError for missing required elements
        with pytest.raises(ValidationError) as exc_info:
            plane._ensure_required_elements_valid()
        assert "Required elements validation failed" in str(exc_info.value)
        
        # Should raise ValidationError for missing conditional requirements
        plane.PixelSpacing = [1.0, 1.0]
        plane.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        plane.ImagePositionPatient = [0.0, 0.0, 0.0]
        # Still missing SliceThickness
        
        with pytest.raises(ValidationError) as exc_info:
            plane._ensure_conditional_requirements_valid()
        assert "Conditional requirements validation failed" in str(exc_info.value)
        
        # Test geometric constraints failure
        plane.SliceThickness = 2.0
        plane.ImageOrientationPatient = [1, 1, 0, 0, 1, 0]  # Not orthogonal
        
        with pytest.raises(ValidationError) as exc_info:
            plane._ensure_geometric_constraints_valid()
        assert "Geometric constraints validation failed" in str(exc_info.value)
    
    def test_zero_copy_validation(self):
        """Test that validation methods use zero-copy optimization."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # The validation methods should work directly with the module instance
        # We can't directly test zero-copy but we can ensure the methods work
        # and return proper ValidationResult objects
        
        results = [
            plane.check_required_elements(),
            plane.check_conditional_requirements(),
            plane.check_enum_constraints(),
            plane.check_sequence_requirements(),
            plane.check_geometric_constraints(),
            plane.check_coordinate_system(),
            plane.validate()
        ]
        
        for result in results:
            assert isinstance(result, ValidationResult)
            # With valid data, most should not have errors
            # (some might have warnings but that's acceptable)


class TestImagePlaneModuleInputValidation:
    """Test that input parameter validation is handled by validators, not module creation."""
    
    def test_module_creation_with_invalid_values(self):
        """Test that module creation succeeds even with invalid values."""
        # Module should create successfully with invalid pixel spacing
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[-1.0, 1.0],  # Negative value
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Module should exist and be usable
        assert plane is not None
        dataset = plane.to_dataset()
        assert dataset.PixelSpacing == [-1.0, 1.0]
        
        # But validation should catch the errors
        result = plane.validate()
        assert result.has_errors
    
    def test_module_creation_with_wrong_dimensions(self):
        """Test that module creation works with wrong dimensions."""
        # Module should create successfully with wrong dimensions
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1],  # Only 5 values
            image_position_patient=[0.0, 0.0],  # Only 2 values
            slice_thickness=2.0
        )
        
        # Module should exist but validation should fail
        assert plane is not None
        dataset = plane.to_dataset()
        assert len(dataset.ImageOrientationPatient) == 5
        assert len(dataset.ImagePositionPatient) == 2
        
        # Validation should catch the errors
        result = plane.validate()
        assert result.has_errors
        assert any("6 direction cosine values" in error for error in result.errors)
        assert any("3 coordinate values" in error for error in result.errors)
    
    def test_pixel_spacing_validation_via_validator(self):
        """Test pixel spacing validation is handled by validator, not module creation."""
        # Module should create successfully even with invalid values
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[-1.0, 1.0],  # Negative value
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Module should be created but validation should catch errors
        result = plane.validate()
        assert result.has_errors
        assert any("must be positive" in error for error in result.errors)
        
        # Test zero pixel spacing
        plane_zero = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.0, 1.0],  # Zero value
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        result_zero = plane_zero.validate()
        assert result_zero.has_errors
        assert any("must be positive" in error for error in result_zero.errors)
    
    def test_image_orientation_validation_via_validator(self):
        """Test image orientation validation is handled by validator, not module creation."""
        # Module should create successfully even with invalid values
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1],  # Only 5 values
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Validation should catch the error
        result = plane.validate()
        assert result.has_errors
        assert any("6 direction cosine values" in error for error in result.errors)
    
    def test_image_position_validation_via_validator(self):
        """Test image position validation is handled by validator, not module creation."""
        # Module should create successfully even with invalid values
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0],  # Only 2 values
            slice_thickness=2.0
        )
        
        # Validation should catch the error
        result = plane.validate()
        assert result.has_errors
        assert any("3 coordinate values" in error for error in result.errors)
    
    def test_paired_requirement_validation(self):
        """Test that Image Position and Image Orientation must be provided as pair."""
        # This is tested implicitly in from_required_elements - both are required parameters
        # Test that validation logic works correctly
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        )
        
        # Both should be present in dataset
        dataset = plane.to_dataset()
        assert hasattr(dataset, 'ImageOrientationPatient')
        assert hasattr(dataset, 'ImagePositionPatient')


class TestImagePlaneModuleProperties:
    """Test module properties and calculations."""
    
    def test_pixel_area_calculation(self):
        """Test pixel area calculation with various spacing values."""
        test_cases = [
            ([1.0, 1.0], 1.0),      # Isotropic 1mm
            ([2.0, 2.0], 4.0),      # Isotropic 2mm  
            ([1.0, 2.0], 2.0),      # Anisotropic
            ([0.5, 0.5], 0.25),     # High resolution
            ([5.0, 2.5], 12.5),     # Large anisotropic
        ]
        
        for spacing, expected_area in test_cases:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=spacing,
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.0
            )
            assert plane.pixel_area is not None
            assert abs(plane.pixel_area - expected_area) < 1e-6
    
    def test_voxel_volume_calculation(self):
        """Test voxel volume calculation with various spacing and thickness values."""
        test_cases = [
            ([1.0, 1.0], 1.0, 1.0),     # 1x1x1 mm
            ([2.0, 2.0], 3.0, 12.0),    # 2x2x3 mm
            ([0.5, 0.5], 1.0, 0.25),    # 0.5x0.5x1 mm
            ([1.0, 2.0], 5.0, 10.0),    # 1x2x5 mm
        ]
        
        for spacing, thickness, expected_volume in test_cases:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=spacing,
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=thickness
            )
            assert plane.voxel_volume is not None
            assert abs(plane.voxel_volume - expected_volume) < 1e-6
    
    def test_geometrically_valid_property(self):
        """Test comprehensive geometric validation property."""
        # Valid geometric configuration
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=2.0
        ).with_optional_elements(spacing_between_slices=2.0)
        
        # All individual checks should pass
        assert plane.is_orthogonal
        assert plane.is_normalized  
        assert plane.has_valid_spacing_between_slices
        assert plane.is_right_handed_coordinate_system
        
        # Overall validation should pass
        assert plane.is_geometrically_valid

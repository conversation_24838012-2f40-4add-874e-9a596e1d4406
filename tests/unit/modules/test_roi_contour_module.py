"""
Test ROIContourModule functionality.

ROIContourModule implements DICOM PS3.3 C.8.8.6 ROI Contour Module.
Defines ROIs as a set of contours with geometric types.
"""

import pydicom
from pyrt_dicom.modules import ROIContourModule
from pyrt_dicom.enums.rt_enums import ContourGeometricType
from pyrt_dicom.validators.modules.roi_contour_validator import ROIContourValidator
from pyrt_dicom.validators import ValidationResult


class TestROIContourModule:
    """Test ROIContourModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        # Create basic contour item
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[10.0, 10.0, 0.0, 20.0, 10.0, 0.0, 20.0, 20.0, 0.0, 10.0, 20.0, 0.0]
        )
        
        # Create ROI contour item
        roi_contour_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        # Create module with required elements
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_contour_item]
        )
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert hasattr(dataset, 'ROIContourSequence')
        assert len(dataset.ROIContourSequence) == 1
        assert dataset.ROIContourSequence[0].ReferencedROINumber == 1
        assert dataset.ROIContourSequence[0].ROIDisplayColor == [255, 0, 0]
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create basic module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )
        
        roi_contour_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[0, 255, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_contour_item]
        ).with_optional_elements()
        
        # Should return self for method chaining
        assert roi_contour is not None
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert hasattr(dataset, 'ROIContourSequence')
    
    def test_create_contour_item_basic(self):
        """Test basic contour item creation."""
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=3,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        assert contour_item.ContourGeometricType == "CLOSED_PLANAR"
        assert contour_item.NumberOfContourPoints == 3
        assert len(contour_item.ContourData) == 9
    
    def test_create_contour_item_with_optional_elements(self):
        """Test contour item creation with optional elements."""
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.OPEN_PLANAR,
            number_of_contour_points=2,
            contour_data=[0.0, 0.0, 0.0, 1.0, 1.0, 1.0],
            contour_number=1,
            contour_slab_thickness=2.5,
            contour_offset_vector=[0.0, 0.0, 1.0]
        )
        
        assert contour_item.ContourGeometricType == "OPEN_PLANAR"
        assert contour_item.NumberOfContourPoints == 2
        assert contour_item.ContourNumber == 1
        assert contour_item.ContourSlabThickness == 2.5
        assert contour_item.ContourOffsetVector == [0.0, 0.0, 1.0]
    
    def test_create_roi_contour_item_basic(self):
        """Test basic ROI contour item creation."""
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=2,
            roi_display_color=[0, 0, 255]
        )
        
        assert roi_item.ReferencedROINumber == 2
        assert roi_item.ROIDisplayColor == [0, 0, 255]
        assert not hasattr(roi_item, 'ContourSequence')  # Optional, not provided
    
    def test_create_roi_contour_item_with_optional_elements(self):
        """Test ROI contour item creation with optional elements."""
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=3,
            roi_display_color=[128, 128, 128],
            recommended_display_grayscale_value=128
        )
        
        assert roi_item.ReferencedROINumber == 3
        assert roi_item.ROIDisplayColor == [128, 128, 128]
        assert roi_item.RecommendedDisplayGrayscaleValue == 128
    
    def test_module_properties(self):
        """Test module convenience properties."""
        # Create module without contours
        empty_module = ROIContourModule.from_required_elements(roi_contour_sequence=[])
        
        assert empty_module.has_contours  # has_contours only checks if ROIContourSequence exists, not if it's empty
        assert empty_module.roi_count == 0
        assert empty_module.total_contour_count == 0
        assert empty_module.get_contour_geometric_types() == []
        
        # Create module with contours
        contour_item1 = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=3,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        contour_item2 = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[5.0, 5.0, 5.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item1, contour_item2]
        )
        
        module_with_contours = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        assert module_with_contours.has_contours
        assert module_with_contours.roi_count == 1
        assert module_with_contours.total_contour_count == 2
        geometric_types = module_with_contours.get_contour_geometric_types()
        assert "CLOSED_PLANAR" in geometric_types
        assert "POINT" in geometric_types
    
    def test_validation_basic(self):
        """Test basic validation functionality."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        validation_result = roi_contour.validate()
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
        
        # Basic validation should pass for well-formed module
        assert len(validation_result.errors) == 0
    
    def test_dataset_generation(self):
        """Test that modules generate valid pydicom Dataset objects."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        # Test dataset generation
        dataset = roi_contour.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        
        # Test that DICOM attributes are accessible through generated dataset
        assert hasattr(dataset, 'ROIContourSequence')
        assert dataset.ROIContourSequence[0].ReferencedROINumber == 1
        assert dataset.ROIContourSequence[0].ROIDisplayColor == [255, 0, 0]
    
    def test_helper_methods(self):
        """Test static helper methods for creating sub-items."""
        # Test contour image item creation
        contour_image_item = ROIContourModule.create_contour_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        assert contour_image_item.ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.2"
        assert contour_image_item.ReferencedSOPInstanceUID == "1.2.3.4.5.6.7.8.9"
        
        # Test source series item creation
        source_series_item = ROIContourModule.create_source_series_item(
            series_instance_uid="1.2.3.4.5.6.7.8.10"
        )
        
        assert source_series_item.SeriesInstanceUID == "1.2.3.4.5.6.7.8.10"
        
        # Test source pixel planes characteristics item creation
        pixel_item = ROIContourModule.create_source_pixel_planes_characteristics_item(
            pixel_spacing=[1.0, 1.0],
            spacing_between_slices=2.5,
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[0.0, 0.0, 0.0],
            number_of_frames=100,
            rows=512,
            columns=512
        )
        
        assert pixel_item.PixelSpacing == [1.0, 1.0]
        assert pixel_item.SpacingBetweenSlices == 2.5
        assert pixel_item.NumberOfFrames == 100
        assert pixel_item.Rows == 512
        assert pixel_item.Columns == 512
    
    def test_validation_result_functionality(self):
        """Test that validator returns ValidationResult with correct functionality."""
        # Create valid test data
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )
        
        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )
        
        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )
        
        # Test direct validator call returns ValidationResult
        dataset = roi_contour.to_dataset()
        validator_result = ROIContourValidator.validate(dataset)
        assert isinstance(validator_result, ValidationResult)
        
        # Test module validate returns dict (backward compatibility)
        assert hasattr(roi_contour, 'validate')
        assert callable(roi_contour.validate)
        
        # Test validation result structure
        validation_result = roi_contour.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)

    def test_check_required_elements_success(self):
        """Test check_required_elements with valid data."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)

    def test_check_required_elements_missing_roi_contour_sequence(self):
        """Test check_required_elements with missing ROI Contour Sequence."""
        # Create empty module
        roi_contour = ROIContourModule.from_required_elements(roi_contour_sequence=[])
        # Remove the sequence to test missing requirement
        del roi_contour['ROIContourSequence']

        result = roi_contour.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("ROI Contour Sequence (3006,0039) is required" in error for error in result.errors)

    def test_check_conditional_requirements(self):
        """Test check_conditional_requirements method."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)

    def test_check_enum_constraints_success(self):
        """Test check_enum_constraints with valid enumerated values."""
        # Create valid module with valid geometric type
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_enum_constraints_invalid_geometric_type(self):
        """Test check_enum_constraints with invalid geometric type."""
        # Create module with invalid geometric type
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # Manually set invalid geometric type
        roi_contour.ROIContourSequence[0].ContourSequence[0].ContourGeometricType = "INVALID_TYPE"

        result = roi_contour.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any("Contour Geometric Type" in warning for warning in result.warnings)

    def test_check_sequence_requirements_success(self):
        """Test check_sequence_requirements with valid sequence structure."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_sequence_requirements_invalid_roi_display_color(self):
        """Test check_sequence_requirements with invalid ROI Display Color."""
        # Create module with invalid ROI Display Color
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0],  # Invalid: only 2 values instead of 3
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("ROI Display Color (3006,002A) must be RGB triplet" in error for error in result.errors)

    def test_check_contour_data_consistency_success(self):
        """Test check_contour_data_consistency with valid contour data."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_contour_data_consistency()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_contour_data_consistency_invalid_data_length(self):
        """Test check_contour_data_consistency with invalid contour data length."""
        # Create module with mismatched contour data length
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0]  # Only 2 points worth of data
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_contour_data_consistency()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("Contour Data length (6) should be 3 times Number of Contour Points (4)" in error for error in result.errors)

    def test_check_source_pixel_planes_usage_success(self):
        """Test check_source_pixel_planes_usage with appropriate usage."""
        # Create valid module without source pixel planes
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_source_pixel_planes_usage()
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) == 0

    def test_check_source_pixel_planes_usage_inappropriate_usage(self):
        """Test check_source_pixel_planes_usage with inappropriate usage."""
        # Create module with source pixel planes but POINT geometric type
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        # Add source pixel planes characteristics
        pixel_item = ROIContourModule.create_source_pixel_planes_characteristics_item(
            pixel_spacing=[1.0, 1.0],
            spacing_between_slices=2.5,
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[0.0, 0.0, 0.0],
            number_of_frames=100,
            rows=512,
            columns=512
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item],
            source_pixel_planes_characteristics_sequence=[pixel_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        result = roi_contour.check_source_pixel_planes_usage()
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) > 0
        assert any("Source Pixel Planes Characteristics Sequence (3006,004A) is present but may not be useful" in warning for warning in result.warnings)

    def test_zero_copy_validation(self):
        """Test that validation methods use zero-copy optimization (pass self instead of self.to_dataset())."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # Test that all validation methods return ValidationResult objects
        # This indirectly tests zero-copy since the validator methods expect Dataset | BaseModule
        result1 = roi_contour.check_required_elements()
        result2 = roi_contour.check_conditional_requirements()
        result3 = roi_contour.check_enum_constraints()
        result4 = roi_contour.check_sequence_requirements()
        result5 = roi_contour.check_contour_data_consistency()
        result6 = roi_contour.check_source_pixel_planes_usage()
        result7 = roi_contour.validate()

        for result in [result1, result2, result3, result4, result5, result6, result7]:
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')

    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data (should not raise exceptions)."""
        # Create valid module
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # These should not raise exceptions for valid data
        try:
            roi_contour._ensure_required_elements_valid()
            roi_contour._ensure_conditional_requirements_valid()
            roi_contour._ensure_enum_constraints_valid()
            roi_contour._ensure_sequence_requirements_valid()
            roi_contour._ensure_contour_data_consistency_valid()
        except Exception as e:
            assert False, f"Private validation methods should not raise exceptions for valid data: {e}"

    def test_private_validation_methods_raise_exceptions(self):
        """Test private validation methods raise ValidationError for invalid data."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with missing required elements
        roi_contour = ROIContourModule.from_required_elements(roi_contour_sequence=[])
        # Remove the sequence to test missing requirement
        del roi_contour['ROIContourSequence']

        # Test that _ensure_required_elements_valid raises ValidationError
        try:
            roi_contour._ensure_required_elements_valid()
            assert False, "_ensure_required_elements_valid should raise ValidationError for missing required elements"
        except ValidationError as e:
            assert "Required elements validation failed" in str(e)
        except Exception as e:
            assert False, f"Expected ValidationError, got {type(e)}: {e}"

    def test_private_validation_methods_enum_constraints_no_exception(self):
        """Test private validation methods with enum constraints (warnings don't raise exceptions)."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with invalid geometric type
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # Manually set invalid geometric type
        roi_contour.ROIContourSequence[0].ContourSequence[0].ContourGeometricType = "INVALID_TYPE"

        # Test that _ensure_enum_constraints_valid does NOT raise ValidationError for warnings
        # (enum validation produces warnings, not errors, so no exception should be raised)
        try:
            roi_contour._ensure_enum_constraints_valid()
            # Should not raise exception for warnings
        except ValidationError as e:
            assert False, f"_ensure_enum_constraints_valid should not raise ValidationError for warnings: {e}"
        except Exception as e:
            assert False, f"Unexpected exception: {type(e)}: {e}"

    def test_private_validation_methods_sequence_requirements_exception(self):
        """Test private validation methods raise ValidationError for invalid sequence requirements."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with invalid ROI Display Color
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.POINT,
            number_of_contour_points=1,
            contour_data=[10.0, 10.0, 0.0]
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0],  # Invalid: only 2 values instead of 3
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # Test that _ensure_sequence_requirements_valid raises ValidationError
        try:
            roi_contour._ensure_sequence_requirements_valid()
            assert False, "_ensure_sequence_requirements_valid should raise ValidationError for invalid sequence requirements"
        except ValidationError as e:
            assert "Sequence structure validation failed" in str(e)
        except Exception as e:
            assert False, f"Expected ValidationError, got {type(e)}: {e}"

    def test_private_validation_methods_contour_data_consistency_exception(self):
        """Test private validation methods raise ValidationError for invalid contour data consistency."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Create module with mismatched contour data length
        contour_item = ROIContourModule.create_contour_item(
            contour_geometric_type=ContourGeometricType.CLOSED_PLANAR,
            number_of_contour_points=4,
            contour_data=[0.0, 0.0, 0.0, 1.0, 0.0, 0.0]  # Only 2 points worth of data
        )

        roi_item = ROIContourModule.create_roi_contour_item(
            referenced_roi_number=1,
            roi_display_color=[255, 0, 0],
            contour_sequence=[contour_item]
        )

        roi_contour = ROIContourModule.from_required_elements(
            roi_contour_sequence=[roi_item]
        )

        # Test that _ensure_contour_data_consistency_valid raises ValidationError
        try:
            roi_contour._ensure_contour_data_consistency_valid()
            assert False, "_ensure_contour_data_consistency_valid should raise ValidationError for invalid contour data"
        except ValidationError as e:
            assert "Contour data consistency validation failed" in str(e)
        except Exception as e:
            assert False, f"Expected ValidationError, got {type(e)}: {e}"
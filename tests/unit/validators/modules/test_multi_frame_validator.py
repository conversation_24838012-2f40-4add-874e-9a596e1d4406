"""
Test MultiFrameValidator functionality and compliance validation.

Tests comprehensive validation of DICOM Multi-frame Module (PS3.3 C.7.6.6)
including Type 1 requirements, frame increment pointer consistency,
stereoscopic pair validation, and conditional logic verification.
"""

import pytest
import pydicom
from pydicom.tag import Tag
from pyrt_dicom.modules import MultiFrameModule
from pyrt_dicom.validators.modules.multi_frame_validator import MultiFrameValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import StereoPairsPresent


class TestMultiFrameValidator:
    """Test MultiFrameValidator compliance validation."""
    
    def test_valid_multi_frame_module_passes_validation(self):
        """Test that a valid multi-frame module passes validation without errors."""
        # Create valid multi-frame module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO,
            encapsulated_pixel_data_value_total_length=2048000
        )
        
        # Validate using dataset (BREAKING CHANGE: must use to_dataset())
        dataset = multi_frame.to_dataset()
        result = MultiFrameValidator.validate(dataset)
        
        # Verify validation passes
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_missing_number_of_frames_generates_error(self):
        """Test validation error for missing Number of Frames (Type 1)."""
        # Create dataset missing NumberOfFrames
        dataset = pydicom.Dataset()
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify Type 1 error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("Number of Frames (0028,0008) is required (Type 1)" in msg for msg in error_messages)
        assert any("Multi-frame images must specify the total number of frames" in msg for msg in error_messages)
    
    def test_invalid_number_of_frames_type_generates_error(self):
        """Test validation passes when NumberOfFrames is properly set."""
        # Create dataset with valid NumberOfFrames
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5  # Valid integer
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        
        result = MultiFrameValidator.validate(dataset)
        
        # Should pass validation
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_zero_number_of_frames_generates_error(self):
        """Test validation error for zero NumberOfFrames value."""
        # Create dataset with zero frames
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 0
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify value error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("must be greater than zero, got 0" in msg for msg in error_messages)
        assert any("DICOM PS3.3 C.7.6.6.1.1" in msg for msg in error_messages)
    
    def test_negative_number_of_frames_generates_error(self):
        """Test validation error for negative NumberOfFrames value."""
        # Create dataset with negative frames
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = -5
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify value error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("must be greater than zero, got -5" in msg for msg in error_messages)
    
    def test_missing_frame_increment_pointer_generates_error(self):
        """Test validation error for missing Frame Increment Pointer (Type 1)."""
        # Create dataset missing FrameIncrementPointer
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify Type 1 error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("Frame Increment Pointer (0028,0009) is required (Type 1)" in msg for msg in error_messages)
        assert any("Even single-frame instances must have FrameIncrementPointer" in msg for msg in error_messages)
        assert any("DICOM PS3.3 C.7.6.6.1.2" in msg for msg in error_messages)
    
    def test_empty_frame_increment_pointer_generates_error(self):
        """Test validation error for empty Frame Increment Pointer list."""
        # Create dataset with empty FrameIncrementPointer
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5
        dataset.FrameIncrementPointer = []
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify empty list error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("must contain at least one DICOM tag" in msg for msg in error_messages)
        assert any("at least one value that points to an attribute" in msg for msg in error_messages)
    
    def test_invalid_frame_increment_pointer_type_generates_error(self):
        """Test validation passes with valid FrameIncrementPointer."""
        # Create dataset with valid FrameIncrementPointer  
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]  # Valid Tag list
        
        result = MultiFrameValidator.validate(dataset)
        
        # Should pass validation
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_enumerated_values_validation(self):
        """Test validation of enumerated values like StereoPairsPresent."""
        # Create dataset with invalid enumerated value
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 6
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "INVALID_VALUE"  # Should be YES or NO
        
        # Test with enumerated values checking enabled
        config = ValidationConfig(check_enumerated_values=True)
        result = MultiFrameValidator.validate(dataset, config)
        
        # Verify enumerated value warning (not error)
        assert result.is_valid  # Enumerated violations are warnings, not errors
        assert len(result.warnings) >= 1
        warning_messages = result.warnings
        assert any("Stereo Pairs Present" in msg for msg in warning_messages)
    
    def test_stereo_pairs_odd_frame_count_warning(self):
        """Test warning for odd frame count with stereo pairs enabled."""
        # Create dataset with odd frames and stereo pairs
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 7  # Odd number
        dataset.FrameIncrementPointer = [Tag(0x5200, 0x9230)]
        dataset.StereoPairsPresent = "YES"
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify warning generated (should be valid but with warning)
        assert result.is_valid  # No errors, but warnings
        assert len(result.warnings) >= 1
        warning_messages = result.warnings
        assert any("is odd" in msg for msg in warning_messages)
        assert any("left/right pairs" in msg for msg in warning_messages)
        assert any("DICOM PS3.3 C.7.6.6.1.3" in msg for msg in warning_messages)
    
    def test_stereo_pairs_insufficient_frames_error(self):
        """Test error for insufficient frames with stereo pairs enabled."""
        # Create dataset with single frame and stereo pairs
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 1
        dataset.FrameIncrementPointer = [Tag(0x5200, 0x9230)]
        dataset.StereoPairsPresent = "YES"
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("At least one complete stereo pair (2 frames minimum)" in msg for msg in error_messages)
        assert any("stereoscopic imaging is enabled" in msg for msg in error_messages)
    
    def test_stereo_pairs_missing_frame_count_error(self):
        """Test error for stereo pairs enabled but missing frame count."""
        # Create dataset with stereo pairs but no NumberOfFrames
        dataset = pydicom.Dataset()
        dataset.FrameIncrementPointer = [Tag(0x5200, 0x9230)]
        dataset.StereoPairsPresent = "YES"
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify error generated
        assert not result.is_valid
        assert len(result.errors) >= 2  # Missing NumberOfFrames + stereo validation
        error_messages = result.errors
        assert any("Cannot validate stereo pair consistency" in msg for msg in error_messages)
    
    def test_frame_time_and_frame_time_vector_warning(self):
        """Test warning for using both Frame Time and Frame Time Vector."""
        # Create dataset with both frame time methods
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063), Tag(0x0018, 0x1065)]  # Both Frame Time tags
        # Add the referenced attributes to avoid missing attribute warnings
        dataset.FrameTime = 100  # milliseconds
        dataset.FrameTimeVector = [100, 100, 100, 100, 100, 100, 100, 100, 100, 100]  # 10 frames
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify warning generated
        assert result.is_valid  # Valid but with warning
        assert len(result.warnings) >= 1
        warning_messages = result.warnings
        assert any("references both Frame Time" in msg for msg in warning_messages)
        assert any("Typically only one" in msg for msg in warning_messages)
    
    def test_large_frame_count_warning(self):
        """Test warning for very large frame counts."""
        # Create dataset with very large frame count
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 15000  # Very large
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify warning generated
        assert result.is_valid  # Valid but with warning
        assert len(result.warnings) >= 1
        warning_messages = result.warnings
        assert any("very large" in msg for msg in warning_messages)
        assert any("performance issues" in msg for msg in warning_messages)
        assert any("DICOM viewers and PACS systems" in msg for msg in warning_messages)
    
    def test_single_frame_stereo_pairs_contradiction_error(self):
        """Test error for single frame with stereo pairs enabled."""
        # Create dataset with contradictory settings
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 1
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "YES"
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify error generated
        assert not result.is_valid
        assert len(result.errors) >= 1
        error_messages = result.errors
        assert any("Single frame (NumberOfFrames=1) cannot have Stereo Pairs Present=YES" in msg for msg in error_messages)
        assert any("Either increase frame count or set StereoPairsPresent to NO" in msg for msg in error_messages)
    
    def test_validation_result_structure(self):
        """Test that ValidationResult objects are properly structured."""
        # Create dataset with multiple issues
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = -1  # Invalid value
        dataset.FrameIncrementPointer = []  # Empty list
        dataset.StereoPairsPresent = "YES"  # Contradicts single frame
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify ValidationResult structure
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        assert hasattr(result, 'is_valid')
        assert isinstance(result.is_valid, bool)
        
        # Should have multiple errors
        assert not result.is_valid
        assert len(result.errors) >= 2
    
    def test_validation_config_enumerated_values_disabled(self):
        """Test validation with enumerated values checking disabled."""
        # Create dataset with invalid enumerated value
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 4
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "INVALID_VALUE"
        
        # Test with enumerated values checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = MultiFrameValidator.validate(dataset, config)
        
        # Should be valid since enumerated checking is disabled
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_frame_increment_pointer_integer_format(self):
        """Test validation with integer format frame increment pointer."""
        # Create dataset with integer FIP (common in pydicom)
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5
        dataset.FrameIncrementPointer = int(Tag(0x0018, 0x1063))  # Integer format
        
        result = MultiFrameValidator.validate(dataset)
        
        # Should be valid - integer format is acceptable
        assert result.is_valid
        assert len(result.errors) == 0
    
    def test_comprehensive_valid_stereoscopic_dataset(self):
        """Test comprehensive validation of valid stereoscopic multi-frame dataset."""
        # Create comprehensive valid stereoscopic dataset
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=20,  # 10 stereo pairs
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_functional_groups()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.YES,
            encapsulated_pixel_data_value_total_length=4096000
        )
        
        dataset = multi_frame.to_dataset()
        result = MultiFrameValidator.validate(dataset)
        
        # Should be completely valid
        assert result.is_valid
        assert len(result.errors) == 0
        
        # Verify all expected attributes are present
        assert dataset.NumberOfFrames == 20
        assert dataset.StereoPairsPresent == "YES"
        assert dataset.EncapsulatedPixelDataValueTotalLength == 4096000
        assert hasattr(dataset, 'FrameIncrementPointer')
    
    def test_validation_error_messages_contain_dicom_references(self):
        """Test that error messages contain specific DICOM tag references and standards."""
        # Create dataset with multiple validation errors
        dataset = pydicom.Dataset()
        # Missing both required Type 1 elements
        
        result = MultiFrameValidator.validate(dataset)
        
        # Verify error messages contain DICOM references
        assert not result.is_valid
        assert len(result.errors) >= 2
        
        error_messages = result.errors
        combined_errors = " ".join(error_messages)
        
        # Check for DICOM tag references
        assert "(0028,0008)" in combined_errors  # NumberOfFrames tag
        assert "(0028,0009)" in combined_errors  # FrameIncrementPointer tag
        
        # Check for DICOM standard references
        assert "Type 1" in combined_errors
        assert "PS3.3" in combined_errors or "C.7.6.6" in combined_errors
    
    def test_edge_case_single_frame_valid(self):
        """Test validation of single frame multi-frame module (edge case)."""
        # Create valid single-frame multi-frame module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=1,  # Single frame
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO
        )
        
        dataset = multi_frame.to_dataset()
        result = MultiFrameValidator.validate(dataset)
        
        # Single frame should be valid when stereo pairs is NO
        assert result.is_valid
        assert len(result.errors) == 0
        assert dataset.NumberOfFrames == 1
        assert dataset.StereoPairsPresent == "NO"
    
    def test_frame_increment_pointer_conversion_valid_formats(self):
        """Test frame increment pointer conversion with valid formats."""
        # Test string format "GGGG,EEEE"
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(["0018,1063"])
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(converted_tags) == 1
        assert converted_tags[0] == int(Tag(0x0018, 0x1063))
        
        # Test string format "GGGGEEEE"
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(["00181063"])
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(converted_tags) == 1
        assert converted_tags[0] == int(Tag(0x0018, 0x1063))
        
        # Test Tag object
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([Tag(0x0018, 0x1063)])
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(converted_tags) == 1
        assert converted_tags[0] == int(Tag(0x0018, 0x1063))
        
        # Test integer format
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([int(Tag(0x0018, 0x1063))])
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(converted_tags) == 1
        assert converted_tags[0] == int(Tag(0x0018, 0x1063))
        
        # Test mixed formats
        mixed_input = ["0018,1063", Tag(0x0018, 0x1065), int(Tag(0x5200, 0x9230))]
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(mixed_input)
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(converted_tags) == 3
    
    def test_frame_increment_pointer_conversion_invalid_formats(self):
        """Test frame increment pointer conversion with invalid formats."""
        # Test empty list
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([])
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "must contain at least one value" in result.errors[0]
        assert len(converted_tags) == 0
        
        # Test invalid string format
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(["invalid"])
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "invalid string format" in result.errors[0]
        assert len(converted_tags) == 0
        
        # Test invalid hex values - will be caught as invalid format first
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(["GGGG,EEEE"])
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "invalid string format" in result.errors[0]  # Caught at format validation level
        assert len(converted_tags) == 0
        
        # Test proper hex format but values that will cause range errors during conversion
        # This will need to be tested directly against the conversion logic since
        # the format validator checks length first
        
        # Test invalid type
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([12.5])  # Float instead of int
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "invalid type" in result.errors[0]
        assert len(converted_tags) == 0
        
        # Test negative integer
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([-1])
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "outside valid DICOM tag range" in result.errors[0]
        assert len(converted_tags) == 0
        
        # Test integer too large
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer([0x100000000])  # Too large for 32-bit
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "outside valid DICOM tag range" in result.errors[0]
        assert len(converted_tags) == 0
    
    def test_frame_increment_pointer_conversion_partial_success(self):
        """Test frame increment pointer conversion with mixed valid/invalid values."""
        # Mix valid and invalid values
        mixed_input = ["0018,1063", "invalid", Tag(0x0018, 0x1065)]
        converted_tags, result = MultiFrameValidator.convert_frame_increment_pointer(mixed_input)
        
        # Should have errors but no converted tags due to invalid values
        assert not result.is_valid
        assert len(result.errors) >= 1
        assert "invalid string format" in result.errors[0]
        assert len(converted_tags) == 0  # No conversion when validation fails

    # Tests for new granular validation methods (Phase 2 refactor)
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements method with pydicom Dataset."""
        # Create valid dataset
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]

        result = MultiFrameValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_required_elements_with_module(self):
        """Test validate_required_elements method with BaseModule instance."""
        # Create valid module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )

        result = MultiFrameValidator.validate_required_elements(multi_frame)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_required_elements_missing_number_of_frames(self):
        """Test validate_required_elements with missing NumberOfFrames."""
        # Test with Dataset
        dataset = pydicom.Dataset()
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]

        result = MultiFrameValidator.validate_required_elements(dataset)

        assert not result.is_valid
        assert len(result.errors) >= 1
        assert any("Number of Frames (0028,0008) is required (Type 1)" in error for error in result.errors)

    def test_validate_required_elements_invalid_number_of_frames(self):
        """Test validate_required_elements with invalid NumberOfFrames value."""
        # Test with Dataset
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 0  # Invalid: must be > 0
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]

        result = MultiFrameValidator.validate_required_elements(dataset)

        assert not result.is_valid
        assert len(result.errors) >= 1
        assert any("must be greater than zero" in error for error in result.errors)

    def test_validate_required_elements_missing_frame_increment_pointer(self):
        """Test validate_required_elements with missing FrameIncrementPointer."""
        # Test with Dataset
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 5

        result = MultiFrameValidator.validate_required_elements(dataset)

        assert not result.is_valid
        assert len(result.errors) >= 1
        assert any("Frame Increment Pointer (0028,0009) is required (Type 1)" in error for error in result.errors)

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements method with pydicom Dataset."""
        # Create dataset (no Type 1C/2C elements in multi-frame module)
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]

        result = MultiFrameValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_module(self):
        """Test validate_conditional_requirements method with BaseModule instance."""
        # Create valid module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )

        result = MultiFrameValidator.validate_conditional_requirements(multi_frame)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values method with pydicom Dataset."""
        # Create dataset with valid enumerated value
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "NO"

        result = MultiFrameValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_enumerated_values_with_module(self):
        """Test validate_enumerated_values method with BaseModule instance."""
        # Create valid module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO
        )

        result = MultiFrameValidator.validate_enumerated_values(multi_frame)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_enumerated_values_invalid_value(self):
        """Test validate_enumerated_values with invalid enumerated value."""
        # Test with Dataset
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "INVALID_VALUE"

        result = MultiFrameValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        # Enumerated value violations are typically warnings, not errors
        assert len(result.warnings) >= 1 or len(result.errors) >= 1

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures method with pydicom Dataset."""
        # Create dataset (no sequence attributes in multi-frame module)
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]

        result = MultiFrameValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_sequence_structures_with_module(self):
        """Test validate_sequence_structures method with BaseModule instance."""
        # Create valid module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )

        result = MultiFrameValidator.validate_sequence_structures(multi_frame)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_main_validate_method_with_dataset(self):
        """Test main validate method orchestration with pydicom Dataset."""
        # Create valid dataset
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 10
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "NO"

        result = MultiFrameValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_main_validate_method_with_module(self):
        """Test main validate method orchestration with BaseModule instance."""
        # Create valid module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        ).with_optional_elements(
            stereo_pairs_present=StereoPairsPresent.NO
        )

        result = MultiFrameValidator.validate(multi_frame)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validator_independence_external_dataset(self):
        """Test validator independence with external Dataset validation."""
        # Create external dataset (simulating data from dcmread)
        external_dataset = pydicom.Dataset()
        external_dataset.NumberOfFrames = 20
        external_dataset.FrameIncrementPointer = [Tag(0x5200, 0x9230)]  # Functional Groups
        external_dataset.StereoPairsPresent = "YES"

        # Validator should work independently of module classes
        result = MultiFrameValidator.validate(external_dataset)

        assert isinstance(result, ValidationResult)
        assert result.is_valid  # Should be valid
        assert len(result.errors) == 0

    def test_zero_copy_validation_basemodule(self):
        """Test zero-copy validation works correctly with BaseModule."""
        # Create module
        multi_frame = MultiFrameModule.from_required_elements(
            number_of_frames=10,
            frame_increment_pointer=MultiFrameModule.create_frame_increment_pointer_for_frame_time()
        )

        # Test all granular methods work with BaseModule directly
        result1 = MultiFrameValidator.validate_required_elements(multi_frame)
        result2 = MultiFrameValidator.validate_conditional_requirements(multi_frame)
        result3 = MultiFrameValidator.validate_enumerated_values(multi_frame)
        result4 = MultiFrameValidator.validate_sequence_structures(multi_frame)
        result5 = MultiFrameValidator.validate(multi_frame)

        # All should work without requiring dataset conversion
        for result in [result1, result2, result3, result4, result5]:
            assert isinstance(result, ValidationResult)
            assert result.is_valid

    def test_comprehensive_validation_failure_scenarios(self):
        """Test comprehensive validation with multiple failure scenarios."""
        # Create dataset with multiple issues
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = -1  # Invalid value
        dataset.FrameIncrementPointer = []  # Empty list
        dataset.StereoPairsPresent = "YES"  # Contradicts negative frame count

        # Test individual granular methods
        result1 = MultiFrameValidator.validate_required_elements(dataset)
        assert not result1.is_valid
        assert len(result1.errors) >= 2  # NumberOfFrames and FrameIncrementPointer errors

        # Test main validation method
        result2 = MultiFrameValidator.validate(dataset)
        assert not result2.is_valid
        assert len(result2.errors) >= 2

    def test_validation_config_integration(self):
        """Test validation configuration integration with granular methods."""
        # Create dataset with enumerated value issue
        dataset = pydicom.Dataset()
        dataset.NumberOfFrames = 4
        dataset.FrameIncrementPointer = [Tag(0x0018, 0x1063)]
        dataset.StereoPairsPresent = "INVALID_VALUE"

        # Test with enumerated values checking enabled
        config = ValidationConfig(check_enumerated_values=True)
        result = MultiFrameValidator.validate(dataset, config)

        # Should have warnings for enumerated value
        assert len(result.warnings) >= 1 or len(result.errors) >= 1

        # Test with enumerated values checking disabled
        config_disabled = ValidationConfig(check_enumerated_values=False)
        result_disabled = MultiFrameValidator.validate(dataset, config_disabled)

        # Should be valid since enumerated checking is disabled
        assert result_disabled.is_valid
"""
Test OverlayPlaneValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.9.2 Overlay Plane Module validation including
group-specific tag validation, conditional logic, and semantic validation.

BREAKING CHANGES: Tests updated for composition-based architecture where
validators receive datasets generated from modules via to_dataset() method.
"""

import pytest
import numpy as np
from pydicom import Dataset
from pydicom.tag import Tag
from pyrt_dicom.validators.modules.overlay_plane_validator import OverlayPlaneValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import OverlayType, OverlaySubtype
from pyrt_dicom.modules import OverlayPlaneModule


class TestOverlayPlaneValidator:
    """Test OverlayPlaneValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (no overlay groups found)."""
        dataset = Dataset()
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "No overlay plane elements found in dataset" in result.errors[0]
    
    def test_valid_graphics_overlay_passes_validation(self):
        """Test that valid graphics overlay passes validation."""
        # Create overlay using module and extract dataset
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        overlay_data[10:22, 10:22] = 1
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_roi_overlay_with_statistics_passes_validation(self):
        """Test that valid ROI overlay with statistics passes validation."""
        overlay_data = np.zeros((64, 64), dtype=np.uint8)
        overlay_data[20:44, 20:44] = 1  # 24x24 ROI
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=64,
            overlay_columns=64,
            overlay_type=OverlayType.ROI,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=576,  # 24x24 = 576 pixels
            roi_mean=150.5,
            roi_standard_deviation=12.3
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_missing_required_elements_fails_validation(self):
        """Test that missing required elements fails validation."""
        dataset = Dataset()
        # Add only partial overlay elements to trigger Type 1 validation
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # OverlayRows only
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors for missing required elements
        assert len(result.errors) >= 6  # Missing 6 other required elements
    
    def test_invalid_overlay_type_fails_validation(self):
        """Test that invalid overlay type fails enumerated value validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset manually with invalid overlay type
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid overlay type
        error_found = any("Overlay Type" in error and "must be one of" in error 
                         for error in result.errors)
        assert error_found
    
    def test_invalid_overlay_bits_allocated_fails_validation(self):
        """Test that invalid overlay bits allocated fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid bits allocated
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 8)  # Invalid - should be 1
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid bits allocated
        error_found = any("Overlay Bits Allocated" in error and "must be 1" in error 
                         for error in result.errors)
        assert error_found
    
    def test_invalid_overlay_bit_position_fails_validation(self):
        """Test that invalid overlay bit position fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid bit position
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 3)  # Invalid - should be 0
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid bit position
        error_found = any("Overlay Bit Position" in error and "must be 0" in error 
                         for error in result.errors)
        assert error_found
    
    def test_insufficient_overlay_data_fails_validation(self):
        """Test that insufficient overlay data fails validation."""
        overlay_data = np.zeros((8, 8), dtype=np.uint8)  # Too small for 32x32 (64 bytes < 128 bytes needed)
        
        # Create dataset with insufficient data
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # 32 rows
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)  # 32 columns
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())  # Too small
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for insufficient data size
        error_found = any("Overlay Data" in error and "size" in error and "less than required" in error 
                         for error in result.errors)
        assert error_found
    
    def test_roi_statistics_with_graphics_overlay_generates_warning(self):
        """Test that ROI statistics with graphics overlay generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,  # Graphics, not ROI
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=100,  # ROI stats on graphics overlay
            roi_mean=128.0,
            roi_standard_deviation=15.0
        )
        
        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)
        
        # Should pass validation but generate warning
        assert result.is_valid  # No errors
        assert result.has_warnings
        # Should have warning about ROI stats on graphics overlay
        warning_found = any("ROI statistics are present" in warning and "not 'R' (ROI)" in warning 
                           for warning in result.warnings)
        assert warning_found
    
    def test_negative_roi_area_fails_validation(self):
        """Test that negative ROI area fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with negative ROI area
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'R')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x1301), 'IS', '-100')  # Negative ROI area
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for negative ROI area
        error_found = any("ROI Area" in error and "must be non-negative" in error 
                         for error in result.errors)
        assert error_found
    
    def test_validation_config_controls_enumerated_value_checking(self):
        """Test that ValidationConfig controls enumerated value checking."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with invalid overlay type
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        # Test with enumerated value checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = OverlayPlaneValidator.validate(dataset, config)
        
        # Should not have enumerated value errors when checking is disabled
        enum_error_found = any("Overlay Type" in error and "must be one of" in error 
                              for error in result.errors)
        assert not enum_error_found
        
        # Test with enumerated value checking enabled (default)
        config = ValidationConfig(check_enumerated_values=True)
        result = OverlayPlaneValidator.validate(dataset, config)
        
        # Should have enumerated value errors when checking is enabled
        enum_error_found = any("Overlay Type" in error and "must be one of" in error 
                              for error in result.errors)
        assert enum_error_found
    
    def test_multiple_overlay_groups_validation(self):
        """Test validation of multiple overlay groups in same dataset."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create dataset with two overlay groups
        dataset = Dataset()
        
        # First overlay group (6000)
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        
        # Second overlay group (6002) with error
        dataset.add_new(Tag(0x6002, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6002, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6002, 0x0040), 'CS', 'X')  # Invalid type
        dataset.add_new(Tag(0x6002, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6002, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6002, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6002, 0x3000), 'OW', overlay_data.tobytes())
        
        result = OverlayPlaneValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid overlay type in group 6002
        error_found = any("6002" in error and "Overlay Type" in error
                         for error in result.errors)
        assert error_found

    def test_multi_frame_overlay_conditional_logic_validation(self):
        """Test multi-frame overlay conditional logic validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Test overlay with multi-frame elements
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_multi_frame_overlay(
            number_of_frames_in_overlay=3,
            image_frame_origin=[1, 5, 10]
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors

    def test_incomplete_multi_frame_elements_generates_warning(self):
        """Test that incomplete multi-frame elements generate warnings."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with only Number of Frames, missing Image Frame Origin
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '3')  # Number of Frames only

        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about incomplete multi-frame elements
        warning_found = any("Number of Frames in Overlay" in warning and "missing" in warning
                           for warning in result.warnings)
        assert warning_found

    def test_invalid_frame_count_fails_validation(self):
        """Test that invalid frame count fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with invalid frame count
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '0')  # Invalid frame count
        dataset.add_new(Tag(0x6000, 0x0051), 'IS', ['1'])

        result = OverlayPlaneValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid frame count
        error_found = any("Number of Frames in Overlay" in error and "must be positive" in error
                         for error in result.errors)
        assert error_found

    def test_invalid_frame_origin_fails_validation(self):
        """Test that invalid frame origin fails validation."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with invalid frame origin (0-based instead of 1-based)
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'G')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x0015), 'IS', '3')
        dataset.add_new(Tag(0x6000, 0x0051), 'IS', ['0', '1', '2'])  # Invalid 0-based

        result = OverlayPlaneValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        # Should have error for invalid frame origin
        error_found = any("Image Frame Origin" in error and "must be >= 1" in error
                         for error in result.errors)
        assert error_found

    def test_active_image_area_subtype_semantic_validation(self):
        """Test semantic validation for ACTIVE_IMAGE_AREA subtype."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.ROI,  # Appropriate for active image area
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.ACTIVE_IMAGE_AREA
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors
        # May have informational messages about active image area usage

    def test_active_image_area_with_graphics_generates_warning(self):
        """Test that ACTIVE_IMAGE_AREA with graphics overlay generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,  # Not typical for active image area
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.ACTIVE_IMAGE_AREA
        )

        dataset = overlay.to_dataset()
        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about active image area with graphics
        warning_found = any("ACTIVE_IMAGE_AREA" in warning and "typically used with ROI" in warning
                           for warning in result.warnings)
        assert warning_found

    def test_roi_area_exceeds_overlay_size_generates_warning(self):
        """Test that ROI area exceeding overlay size generates warning."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)

        # Create dataset with ROI area larger than overlay
        dataset = Dataset()
        dataset.add_new(Tag(0x6000, 0x0010), 'US', 32)  # 32x32 = 1024 pixels max
        dataset.add_new(Tag(0x6000, 0x0011), 'US', 32)
        dataset.add_new(Tag(0x6000, 0x0040), 'CS', 'R')
        dataset.add_new(Tag(0x6000, 0x0050), 'SS', [1, 1])
        dataset.add_new(Tag(0x6000, 0x0100), 'US', 1)
        dataset.add_new(Tag(0x6000, 0x0102), 'US', 0)
        dataset.add_new(Tag(0x6000, 0x3000), 'OW', overlay_data.tobytes())
        dataset.add_new(Tag(0x6000, 0x1301), 'IS', '2000')  # Exceeds 1024

        result = OverlayPlaneValidator.validate(dataset)

        # Should pass validation but generate warning
        assert result.is_valid
        assert result.has_warnings
        # Should have warning about ROI area exceeding overlay size
        warning_found = any("ROI Area" in warning and "exceeds total overlay area" in warning
                           for warning in result.warnings)
        assert warning_found

    def test_overlay_group_constraints_validation(self):
        """Test overlay group constraints validation."""
        from pyrt_dicom.validators.validation_result import ValidationResult
        
        # Test valid overlay group
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x6000, result)
        assert result.is_valid
        assert not result.has_errors
        
        # Test another valid overlay group
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x601E, result)
        assert result.is_valid
        assert not result.has_errors

    def test_overlay_group_below_range_fails_validation(self):
        """Test that overlay group below valid range fails validation."""
        from pyrt_dicom.validators.validation_result import ValidationResult
        
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x5FFF, result)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for group outside valid range
        error_found = any("0x5FFF" in error and "outside valid range" in error
                         for error in result.errors)
        assert error_found

    def test_overlay_group_above_range_fails_validation(self):
        """Test that overlay group above valid range fails validation."""
        from pyrt_dicom.validators.validation_result import ValidationResult
        
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x601F, result)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for group outside valid range
        error_found = any("0x601F" in error and "outside valid range" in error
                         for error in result.errors)
        assert error_found

    def test_overlay_group_odd_number_fails_validation(self):
        """Test that odd overlay group numbers fail validation."""
        from pyrt_dicom.validators.validation_result import ValidationResult
        
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x6001, result)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for odd group number
        error_found = any("0x6001" in error and "not an even number" in error
                         for error in result.errors)
        assert error_found

    def test_multiple_overlay_group_constraint_errors(self):
        """Test that multiple overlay group constraint errors are detected."""
        from pyrt_dicom.validators.validation_result import ValidationResult
        
        # Test group that is both outside range AND odd (below range takes precedence)
        result = ValidationResult()
        OverlayPlaneValidator._validate_overlay_group_constraints(0x5FFD, result)  # Below range and odd
        
        assert not result.is_valid
        assert result.has_errors
        # Should have error for range (odd number check won't be reached due to elif)
        error_found = any("0x5FFD" in error and "outside valid range" in error
                         for error in result.errors)
        assert error_found

    # Test new granular validation methods with both Dataset and BaseModule
    def test_validate_required_elements_with_dataset_and_module(self):
        """Test validate_required_elements with both Dataset and BaseModule."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Test with BaseModule (zero-copy)
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Test BaseModule validation
        result_module = OverlayPlaneValidator.validate_required_elements(overlay_module)
        assert isinstance(result_module, ValidationResult)
        assert result_module.is_valid
        assert not result_module.has_errors
        
        # Test with Dataset
        dataset = overlay_module.to_dataset()
        result_dataset = OverlayPlaneValidator.validate_required_elements(dataset)
        assert isinstance(result_dataset, ValidationResult)
        assert result_dataset.is_valid
        assert not result_dataset.has_errors
        
        # Results should be equivalent
        assert len(result_module.errors) == len(result_dataset.errors)
        assert len(result_module.warnings) == len(result_dataset.warnings)

    def test_validate_conditional_requirements_with_dataset_and_module(self):
        """Test validate_conditional_requirements with both Dataset and BaseModule."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Test with ROI statistics and multi-frame
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.ROI,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=500,
            roi_mean=128.0,
            roi_standard_deviation=15.0
        ).with_multi_frame_overlay(
            number_of_frames_in_overlay=3,
            image_frame_origin=[1, 5, 10]
        )
        
        # Test BaseModule validation
        result_module = OverlayPlaneValidator.validate_conditional_requirements(overlay_module)
        assert isinstance(result_module, ValidationResult)
        assert result_module.is_valid
        assert not result_module.has_errors
        
        # Test with Dataset
        dataset = overlay_module.to_dataset()
        result_dataset = OverlayPlaneValidator.validate_conditional_requirements(dataset)
        assert isinstance(result_dataset, ValidationResult)
        assert result_dataset.is_valid
        assert not result_dataset.has_errors

    def test_validate_enumerated_values_with_dataset_and_module(self):
        """Test validate_enumerated_values with both Dataset and BaseModule."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.USER
        )
        
        # Test BaseModule validation
        result_module = OverlayPlaneValidator.validate_enumerated_values(overlay_module)
        assert isinstance(result_module, ValidationResult)
        assert result_module.is_valid
        assert not result_module.has_errors
        
        # Test with Dataset
        dataset = overlay_module.to_dataset()
        result_dataset = OverlayPlaneValidator.validate_enumerated_values(dataset)
        assert isinstance(result_dataset, ValidationResult)
        assert result_dataset.is_valid
        assert not result_dataset.has_errors

    def test_validate_sequence_structures_with_dataset_and_module(self):
        """Test validate_sequence_structures with both Dataset and BaseModule."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.ROI,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_optional_elements(
            overlay_subtype=OverlaySubtype.ACTIVE_IMAGE_AREA
        )
        
        # Test BaseModule validation
        result_module = OverlayPlaneValidator.validate_sequence_structures(overlay_module)
        assert isinstance(result_module, ValidationResult)
        assert result_module.is_valid
        assert not result_module.has_errors
        
        # Test with Dataset
        dataset = overlay_module.to_dataset()
        result_dataset = OverlayPlaneValidator.validate_sequence_structures(dataset)
        assert isinstance(result_dataset, ValidationResult)
        assert result_dataset.is_valid
        assert not result_dataset.has_errors

    def test_granular_methods_with_missing_overlay_groups(self):
        """Test granular validation methods with missing overlay groups."""
        # Empty dataset with no overlay groups
        empty_dataset = Dataset()
        
        # Test all granular methods with empty dataset
        result_required = OverlayPlaneValidator.validate_required_elements(empty_dataset)
        assert not result_required.is_valid
        assert result_required.has_errors
        assert "No overlay plane elements found" in result_required.errors[0]
        
        result_conditional = OverlayPlaneValidator.validate_conditional_requirements(empty_dataset)
        assert result_conditional.is_valid  # No conditional requirements to check without overlay groups
        assert not result_conditional.has_errors
        
        result_enum = OverlayPlaneValidator.validate_enumerated_values(empty_dataset)
        assert result_enum.is_valid  # No enums to check without overlay groups
        assert not result_enum.has_errors
        
        result_sequence = OverlayPlaneValidator.validate_sequence_structures(empty_dataset)
        assert result_sequence.is_valid  # No sequences to check without overlay groups
        assert not result_sequence.has_errors

    def test_main_validate_method_orchestrates_granular_methods(self):
        """Test that main validate method properly orchestrates granular methods."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        )
        
        # Test main validate method with BaseModule
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        
        result = OverlayPlaneValidator.validate(overlay_module, config)
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        
        # Test with Dataset as well
        dataset = overlay_module.to_dataset()
        result_dataset = OverlayPlaneValidator.validate(dataset, config)
        assert isinstance(result_dataset, ValidationResult)
        assert result_dataset.is_valid
        assert not result_dataset.has_errors

    def test_conditional_validation_with_roi_statistics_mismatch(self):
        """Test conditional validation detects ROI statistics with Graphics overlay."""
        overlay_data = np.zeros((32, 32), dtype=np.uint8)
        
        # Create Graphics overlay with ROI statistics (should generate warning)
        overlay_module = OverlayPlaneModule.from_required_elements(
            overlay_rows=32,
            overlay_columns=32,
            overlay_type=OverlayType.GRAPHICS,  # Graphics but with ROI stats
            overlay_origin=[1, 1],
            overlay_data=overlay_data.tobytes()
        ).with_roi_statistics(
            roi_area=500,
            roi_mean=128.0,
            roi_standard_deviation=15.0
        )
        
        # Test conditional validation
        result = OverlayPlaneValidator.validate_conditional_requirements(overlay_module)
        assert isinstance(result, ValidationResult)
        assert result.is_valid  # No errors, but warnings expected
        assert result.has_warnings
        
        # Check for specific warning about ROI stats with Graphics
        warning_found = any("ROI statistics are present" in warning and "not 'R' (ROI)" in warning
                           for warning in result.warnings)
        assert warning_found

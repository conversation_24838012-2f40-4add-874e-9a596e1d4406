"""Overlay Plane Module DICOM validation - PS3.3 C.9.2

This validator implements comprehensive validation for the DICOM Overlay Plane Module
according to PS3.3 Section C.9.2, including all conditional logic requirements and
group-specific tag handling for overlay planes (60xx group elements).

Key validation areas:
- Type 1 required elements validation
- Overlay group-specific tag validation (6000-601E)
- ROI statistics conditional logic (only for ROI overlays)
- Multi-frame overlay conditional logic
- Overlay data size consistency validation
- Enumerated value validation for overlay types and subtypes
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from pydicom.tag import Tag
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import OverlayType, OverlaySubtype

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class OverlayPlaneValidator(BaseValidator):
    """Validator for DICOM Overlay Plane Module (PS3.3 C.9.2).

    Provides comprehensive validation of overlay plane modules including:
    - Group-specific overlay tag validation (60xx elements)
    - Conditional logic for ROI statistics and multi-frame overlays
    - DICOM standard compliance for overlay data and attributes
    - Semantic validation of overlay types and subtypes
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Find all overlay groups present in the dataset
        overlay_groups = OverlayPlaneValidator._find_overlay_groups(data)
        
        if not overlay_groups:
            result.add_error(
                "No overlay plane elements found in dataset. "
                "Overlay Plane Module requires at least one overlay group (60xx elements)."
            )
            return result
        
        # Validate each overlay group's constraints and required elements
        for group in overlay_groups:
            OverlayPlaneValidator._validate_overlay_group_constraints(group, result)
            OverlayPlaneValidator._validate_type1_requirements(data, group, result)
            
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Find all overlay groups present in the dataset
        overlay_groups = OverlayPlaneValidator._find_overlay_groups(data)
        
        # Validate each overlay group's conditional requirements
        for group in overlay_groups:
            OverlayPlaneValidator._validate_roi_statistics_conditional_logic(data, group, result)
            OverlayPlaneValidator._validate_multi_frame_conditional_logic(data, group, result)
            
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Find all overlay groups present in the dataset
        overlay_groups = OverlayPlaneValidator._find_overlay_groups(data)
        
        # Validate each overlay group's enumerated values
        for group in overlay_groups:
            OverlayPlaneValidator._validate_enumerated_values(data, group, result)
            
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        
        # Find all overlay groups present in the dataset
        overlay_groups = OverlayPlaneValidator._find_overlay_groups(data)
        
        # Validate each overlay group's data consistency and overlay subtype semantics
        for group in overlay_groups:
            OverlayPlaneValidator._validate_overlay_data_consistency(data, group, result)
            OverlayPlaneValidator._validate_overlay_subtype_semantics(data, group, result)
            
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(OverlayPlaneValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(OverlayPlaneValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(OverlayPlaneValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(OverlayPlaneValidator.validate_sequence_structures(data))
        
        return result

    @staticmethod
    def _find_overlay_groups(data: Union[Dataset, 'BaseModule']) -> list[int]:
        """Find all potential overlay groups present in the dataset.
        
        This method finds groups that contain overlay-like tags, regardless of 
        whether the group number itself is valid. This allows validation to 
        catch and report invalid overlay group numbers.

        Args:
            data: pydicom Dataset OR BaseModule instance to search for overlay elements

        Returns:
            list[int]: List of overlay group numbers (e.g., [0x6000, 0x6002])
        """
        overlay_groups = set()

        # For BaseModule, we need to get the keys from the internal dataset
        # For Dataset, we can use keys() directly
        if hasattr(data, '_dataset'):
            # BaseModule case - access internal dataset
            keys = data._dataset.keys()
        else:
            # Dataset case
            keys = data.keys()

        # Search for overlay-specific tags in the dataset
        # Look for any group that has overlay-type elements (60xx,xxxx pattern)
        for tag in keys:
            group = tag.group
            element = tag.element
            
            # Check if this looks like an overlay group (potential overlay range with overlay elements)
            # We cast a wider net to catch invalid groups that attempt to use overlay elements
            if (0x5000 <= group <= 0x6FFF and 
                element in [0x0010, 0x0011, 0x0040, 0x0050, 0x0100, 0x0102, 0x3000]):
                overlay_groups.add(group)

        return sorted(list(overlay_groups))

    @staticmethod
    def _validate_overlay_group_constraints(group: int, result: ValidationResult) -> None:
        """Validate overlay group number constraints per DICOM PS3.3 C.9.2.
        
        Overlay groups must be even numbers between 0x6000 and 0x601E.
        
        Args:
            group: Overlay group number to validate
            result: ValidationResult to accumulate errors
        """
        if group < 0x6000 or group > 0x601E:
            result.add_error(
                f"Overlay group 0x{group:04X} is outside valid range. "
                f"Overlay groups must be between 0x6000 and 0x601E per DICOM PS3.3 C.9.2."
            )
        elif group % 2 != 0:
            result.add_error(
                f"Overlay group 0x{group:04X} is not an even number. "
                f"Overlay groups must be even numbers per DICOM PS3.3 C.9.2."
            )


    @staticmethod
    def _validate_type1_requirements(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate Type 1 (required) attributes for specific overlay group.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors
        """
        group_hex = f"{group:04X}"

        # Define required tags for this overlay group
        required_tags = [
            (Tag(group, 0x0010), f"Overlay Rows ({group_hex},0010)"),
            (Tag(group, 0x0011), f"Overlay Columns ({group_hex},0011)"),
            (Tag(group, 0x0040), f"Overlay Type ({group_hex},0040)"),
            (Tag(group, 0x0050), f"Overlay Origin ({group_hex},0050)"),
            (Tag(group, 0x0100), f"Overlay Bits Allocated ({group_hex},0100)"),
            (Tag(group, 0x0102), f"Overlay Bit Position ({group_hex},0102)"),
            (Tag(group, 0x3000), f"Overlay Data ({group_hex},3000)")
        ]

        # Check presence of required elements using 'in' operator
        for tag, description in required_tags:
            if tag not in data:
                result.add_error(f"{description} is required (Type 1) for overlay group {group_hex}")

        # Validate specific Type 1 constraints
        rows_tag = Tag(group, 0x0010)
        if rows_tag in data:
            rows_value = data[rows_tag].value
            if not isinstance(rows_value, int) or rows_value < 1:
                result.add_error(
                    f"Overlay Rows ({group_hex},0010) must be a positive integer, "
                    f"got {rows_value} of type {type(rows_value).__name__}"
                )

        cols_tag = Tag(group, 0x0011)
        if cols_tag in data:
            cols_value = data[cols_tag].value
            if not isinstance(cols_value, int) or cols_value < 1:
                result.add_error(
                    f"Overlay Columns ({group_hex},0011) must be a positive integer, "
                    f"got {cols_value} of type {type(cols_value).__name__}"
                )

        origin_tag = Tag(group, 0x0050)
        if origin_tag in data:
            origin = data[origin_tag].value
            # Handle pydicom MultiValue objects as well as lists and tuples
            if not hasattr(origin, '__len__') or len(origin) != 2:
                result.add_error(
                    f"Overlay Origin ({group_hex},0050) must be a pair of values [row, column], "
                    f"got {origin}"
                )
            else:
                try:
                    _, _ = int(origin[0]), int(origin[1])
                    # Note: DICOM allows values < 1 to indicate overlay extends beyond image
                except (ValueError, TypeError):
                    result.add_error(
                        f"Overlay Origin ({group_hex},0050) values must be integers, "
                        f"got [{origin[0]}, {origin[1]}]"
                    )

        bits_alloc_tag = Tag(group, 0x0100)
        if bits_alloc_tag in data:
            bits_value = data[bits_alloc_tag].value
            if bits_value != 1:
                result.add_error(
                    f"Overlay Bits Allocated ({group_hex},0100) must be 1 per DICOM standard, "
                    f"got {bits_value}. Embedding overlay data in pixel data is retired."
                )

        bit_pos_tag = Tag(group, 0x0102)
        if bit_pos_tag in data:
            pos_value = data[bit_pos_tag].value
            if pos_value != 0:
                result.add_error(
                    f"Overlay Bit Position ({group_hex},0102) must be 0 per DICOM standard, "
                    f"got {pos_value}. Embedding overlay data in pixel data is retired."
                )
    
    @staticmethod
    def _validate_enumerated_values(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard for specific overlay group.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors
        """
        group_hex = f"{group:04X}"

        # Overlay Type validation
        type_tag = Tag(group, 0x0040)
        if type_tag in data:
            overlay_type = data[type_tag].value
            valid_types = [e.value for e in OverlayType]
            if overlay_type not in valid_types:
                result.add_error(
                    f"Overlay Type ({group_hex},0040) must be one of {valid_types}, "
                    f"got '{overlay_type}'. Valid values: 'G' (Graphics) or 'R' (ROI)."
                )

        # Overlay Subtype validation
        subtype_tag = Tag(group, 0x0045)
        if subtype_tag in data:
            overlay_subtype = data[subtype_tag].value
            valid_subtypes = [e.value for e in OverlaySubtype]
            if overlay_subtype not in valid_subtypes:
                result.add_error(
                    f"Overlay Subtype ({group_hex},0045) must be one of {valid_subtypes}, "
                    f"got '{overlay_subtype}'. See DICOM PS3.3 C.9.2.1.3 for valid defined terms."
                )
    
    @staticmethod
    def _validate_overlay_data_consistency(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate overlay data consistency and size for specific overlay group.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        rows_tag = Tag(group, 0x0010)
        cols_tag = Tag(group, 0x0011)
        data_tag = Tag(group, 0x3000)

        # Check if all required elements are present using 'in' operator
        if not all(tag in data for tag in [rows_tag, cols_tag, data_tag]):
            return  # Type 1 validation will catch missing elements

        # Calculate expected data size
        rows = data[rows_tag].value
        cols = data[cols_tag].value
        total_pixels = rows * cols
        expected_bits = total_pixels
        expected_bytes = (expected_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length for OW VR
        expected_bytes_padded = expected_bytes + (expected_bytes % 2)

        overlay_data = data[data_tag].value
        actual_bytes = len(overlay_data)

        if actual_bytes < expected_bytes:
            result.add_error(
                f"Overlay Data ({group_hex},3000) size ({actual_bytes} bytes) is less than required "
                f"for {rows}x{cols} overlay (minimum {expected_bytes} bytes). "
                f"Each overlay pixel requires 1 bit, packed into bytes."
            )
        elif actual_bytes != expected_bytes_padded:
            result.add_warning(
                f"Overlay Data ({group_hex},3000) size ({actual_bytes} bytes) differs from expected "
                f"padded size ({expected_bytes_padded} bytes) for {rows}x{cols} overlay. "
                f"DICOM OW VR requires even byte length."
            )

    @staticmethod
    def _validate_roi_statistics_conditional_logic(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate ROI statistics conditional logic per DICOM PS3.3 C.9.2.1.2.

        ROI statistics (ROI Area, ROI Mean, ROI Standard Deviation) are only meaningful
        when Overlay Type is 'R' (ROI). This implements the conditional logic from the
        DICOM standard.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        type_tag = Tag(group, 0x0040)
        roi_area_tag = Tag(group, 0x1301)
        roi_mean_tag = Tag(group, 0x1302)
        roi_std_tag = Tag(group, 0x1303)

        roi_stats_tags = [roi_area_tag, roi_mean_tag, roi_std_tag]
        has_roi_stats = any(tag in data for tag in roi_stats_tags)

        # Check overlay type
        overlay_type = None
        if type_tag in data:
            overlay_type = data[type_tag].value

        # Validate conditional logic
        if has_roi_stats:
            if overlay_type != "R":
                result.add_warning(
                    f"ROI statistics are present in overlay group {group_hex} but Overlay Type "
                    f"({group_hex},0040) is '{overlay_type}', not 'R' (ROI). "
                    f"ROI statistics are only meaningful for ROI overlays per DICOM PS3.3 C.9.2.1.2."
                )

        # Validate individual ROI statistics if present
        if roi_area_tag in data:
            try:
                roi_area = int(data[roi_area_tag].value)
                if roi_area < 0:
                    result.add_error(
                        f"ROI Area ({group_hex},1301) must be non-negative, got {roi_area}"
                    )

                # Cross-validate with overlay size if available
                rows_tag = Tag(group, 0x0010)
                cols_tag = Tag(group, 0x0011)
                if rows_tag in data and cols_tag in data:
                    max_area = data[rows_tag].value * data[cols_tag].value
                    if roi_area > max_area:
                        result.add_warning(
                            f"ROI Area ({roi_area}) exceeds total overlay area ({max_area}) "
                            f"for overlay group {group_hex}"
                        )
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Area ({group_hex},1301) must be an integer, "
                    f"got {data[roi_area_tag].value}"
                )

        if roi_mean_tag in data:
            try:
                _ = float(data[roi_mean_tag].value)
                # No specific constraints on ROI mean value per DICOM standard
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Mean ({group_hex},1302) must be numeric, "
                    f"got {data[roi_mean_tag].value}"
                )

        if roi_std_tag in data:
            try:
                roi_std = float(data[roi_std_tag].value)
                if roi_std < 0:
                    result.add_error(
                        f"ROI Standard Deviation ({group_hex},1303) must be non-negative, "
                        f"got {roi_std}"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"ROI Standard Deviation ({group_hex},1303) must be numeric, "
                    f"got {data[roi_std_tag].value}"
                )

    @staticmethod
    def _validate_multi_frame_conditional_logic(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate multi-frame overlay conditional logic per DICOM PS3.3 C.9.2.1.4.

        When Number of Frames in Overlay (60xx,0015) and Image Frame Origin (60xx,0051)
        are absent, the overlay SHALL be applied to all frames in the multi-frame image.
        When present, they define frame-specific overlay behavior.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        frames_tag = Tag(group, 0x0015)
        origin_tag = Tag(group, 0x0051)

        has_frames = frames_tag in data
        has_origin = origin_tag in data

        # Validate consistency of multi-frame elements
        if has_frames and not has_origin:
            result.add_warning(
                f"Number of Frames in Overlay ({group_hex},0015) is present but "
                f"Image Frame Origin ({group_hex},0051) is missing. "
                f"Both elements should be present together for multi-frame overlays."
            )
        elif has_origin and not has_frames:
            result.add_warning(
                f"Image Frame Origin ({group_hex},0051) is present but "
                f"Number of Frames in Overlay ({group_hex},0015) is missing. "
                f"Both elements should be present together for multi-frame overlays."
            )

        # Validate frame count if present
        if has_frames:
            try:
                frame_count = int(data[frames_tag].value)
                if frame_count < 1:
                    result.add_error(
                        f"Number of Frames in Overlay ({group_hex},0015) must be positive, "
                        f"got {frame_count}"
                    )
            except (ValueError, TypeError):
                result.add_error(
                    f"Number of Frames in Overlay ({group_hex},0015) must be an integer, "
                    f"got {data[frames_tag].value}"
                )

        # Validate frame origins if present
        if has_origin:
            try:
                frame_origins = data[origin_tag].value
                if not hasattr(frame_origins, '__len__'):
                    frame_origins = [frame_origins]  # Handle single value

                for i, origin in enumerate(frame_origins):
                    frame_num = int(origin)
                    if frame_num < 1:
                        result.add_error(
                            f"Image Frame Origin ({group_hex},0051) frame {i+1} must be >= 1 "
                            f"(1-based frame numbering), got {frame_num}"
                        )
            except (ValueError, TypeError):
                result.add_error(
                    f"Image Frame Origin ({group_hex},0051) must contain integer frame numbers, "
                    f"got {data[origin_tag].value}"
                )

    @staticmethod
    def _validate_overlay_subtype_semantics(data: Union[Dataset, 'BaseModule'], group: int, result: ValidationResult) -> None:
        """Validate overlay subtype semantic requirements per DICOM PS3.3 C.9.2.1.3.

        Different overlay subtypes have specific semantic requirements and use cases.
        This validates that the subtype is appropriate for the overlay type and context.

        Args:
            data: pydicom Dataset OR BaseModule instance containing overlay elements
            group: Overlay group number (e.g., 0x6000)
            result: ValidationResult to accumulate errors and warnings
        """
        group_hex = f"{group:04X}"

        type_tag = Tag(group, 0x0040)
        subtype_tag = Tag(group, 0x0045)

        if subtype_tag not in data:
            return  # Subtype is Type 3 (optional)

        overlay_type = data[type_tag].value if type_tag in data else None
        overlay_subtype = data[subtype_tag].value

        # Validate ACTIVE_IMAGE_AREA subtype special requirements
        if overlay_subtype == OverlaySubtype.ACTIVE_IMAGE_AREA.value:
            if overlay_type != "R":
                result.add_warning(
                    f"Overlay Subtype 'ACTIVE_IMAGE_AREA' in group {group_hex} is typically "
                    f"used with ROI overlays (Type 'R'), but Overlay Type is '{overlay_type}'. "
                    f"Active image area overlays identify pixels generated from image data acquisition."
                )

            # Additional semantic validation for active image area
            result.add_warning(
                f"Overlay group {group_hex} uses ACTIVE_IMAGE_AREA subtype. "
                f"This overlay should identify all pixels generated from image data acquisition "
                f"with overlay bit value of 1, excluding burned-in annotations per DICOM PS3.3 C.9.2.1.3."
            )



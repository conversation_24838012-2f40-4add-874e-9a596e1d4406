"""Patient Study Module DICOM validation - PS3.3 C.7.2.2

This validator implements comprehensive validation for the Patient Study Module
according to DICOM PS3.3 C.7.2.2 specification, including all conditional
requirements, enumerated values, and sequence structures.
"""

import re
from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class PatientStudyValidator(BaseValidator):
    """Validator for DICOM Patient Study Module (PS3.3 C.7.2.2).

    Validates all Patient Study Module requirements including:
    - Type 2C conditional requirements for non-human organisms
    - Type 2C conditional requirements for sex parameters
    - Enumerated value constraints
    - Sequence structure requirements
    - Cross-field dependencies and semantic constraints
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Note: Patient Study Module has no Type 1 or Type 2 elements - all are Type 2C or Type 3
        # This method is present for API consistency but returns empty result
        
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 2C: Patient's Sex Neutered (0010,2203) required if Patient is a non-human organism
        is_non_human = (hasattr(data, 'PatientSpeciesDescription') or 
                       hasattr(data, 'PatientSpeciesCodeSequence'))

        if is_non_human:
            if not hasattr(data, 'PatientSexNeutered'):
                result.add_error(
                    "Patient's Sex Neutered (0010,2203) is Type 2C - required when Patient is a "
                    "non-human organism. Detected species information but missing sex neutered status. "
                    "Per DICOM PS3.3 C.7.2.2, this field must be present for non-human organisms."
                )

        # Type 2C: Sex Parameters for Clinical Use Category Comment/Reference required for "Specified" code
        if hasattr(data, 'SexParametersForClinicalUseCategorySequence'):
            spcu_seq = data.SexParametersForClinicalUseCategorySequence
            for i, item in enumerate(spcu_seq):
                if hasattr(item, 'SexParametersForClinicalUseCategoryCodeSequence'):
                    code_seq = item.SexParametersForClinicalUseCategoryCodeSequence
                    for j, code_item in enumerate(code_seq):
                        if (hasattr(code_item, 'CodeValue') and code_item.CodeValue == '131232' and
                            hasattr(code_item, 'CodingSchemeDesignator') and code_item.CodingSchemeDesignator == 'DCM'):

                            # Validate Comment (Type 2C)
                            if not hasattr(item, 'SexParametersForClinicalUseCategoryComment'):
                                result.add_error(
                                    f"Sex Parameters for Clinical Use Category Sequence item {i+1}, "
                                    f"Code Sequence item {j+1}: Sex Parameters for Clinical Use Category "
                                    f"Comment (0010,0042) is Type 2C - required when Category Code is "
                                    f"'Specified' (131232, DCM). Per DICOM PS3.3 C.7.2.2, this field "
                                    f"must be present to provide clinical context for the specified parameters."
                                )

                            # Validate Reference (Type 2C)
                            if not hasattr(item, 'SexParametersForClinicalUseCategoryReference'):
                                result.add_error(
                                    f"Sex Parameters for Clinical Use Category Sequence item {i+1}, "
                                    f"Code Sequence item {j+1}: Sex Parameters for Clinical Use Category "
                                    f"Reference (0010,0047) is Type 2C - required when Category Code is "
                                    f"'Specified' (131232, DCM). Per DICOM PS3.3 C.7.2.2, this field "
                                    f"must provide a reference to the source of the specified parameters."
                                )

        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Smoking Status (0010,21A0) - Type 3
        if 'SmokingStatus' in data:
            smoking_status = data.SmokingStatus
            if smoking_status not in ["YES", "NO", "UNKNOWN"]:
                result.add_warning(
                    f"Smoking Status (0010,21A0) has invalid value '{smoking_status}'. "
                    f"Valid values are: YES, NO, UNKNOWN per DICOM PS3.3 C.7.2.2"
                )

        # Pregnancy Status (0010,21C0) - Type 3
        if 'PregnancyStatus' in data:
            pregnancy_status = data.PregnancyStatus
            # Convert to string for validation if it's an integer
            pregnancy_str = str(pregnancy_status)
            if pregnancy_str not in ["1", "2", "3", "4"]:
                result.add_warning(
                    f"Pregnancy Status (0010,21C0) has invalid value '{pregnancy_status}'. "
                    f"Valid values are: 1 (not pregnant), 2 (possibly pregnant), 3 (definitely pregnant), "
                    f"4 (unknown) per DICOM PS3.3 C.7.2.2"
                )

        # Patient's Sex Neutered (0010,2203) - Type 2C
        if 'PatientSexNeutered' in data:
            sex_neutered = data.PatientSexNeutered
            if sex_neutered not in ["ALTERED", "UNALTERED"]:
                result.add_warning(
                    f"Patient's Sex Neutered (0010,2203) has invalid value '{sex_neutered}'. "
                    f"Valid values are: ALTERED, UNALTERED per DICOM PS3.3 C.7.2.2"
                )

        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Gender Identity Sequence (0010,0041) - Type 3
        if hasattr(data, 'GenderIdentitySequence') and data.GenderIdentitySequence:
            gender_identity_seq = data.GenderIdentitySequence
            for i, item in enumerate(gender_identity_seq):
                item_num = i + 1

                # Gender Identity Code Sequence (0010,0044) - Type 1 within sequence
                if not hasattr(item, 'GenderIdentityCodeSequence'):
                    result.add_error(
                        f"Gender Identity Sequence item {item_num}: Gender Identity Code Sequence "
                        f"(0010,0044) is Type 1 - required within each sequence item. "
                        f"Per DICOM PS3.3 C.7.2.2, this field must contain coded gender identity values."
                    )
                else:
                    code_seq = item.GenderIdentityCodeSequence
                    if len(code_seq) != 1:
                        result.add_error(
                            f"Gender Identity Sequence item {item_num}: Gender Identity Code Sequence "
                            f"(0010,0044) must contain exactly one item. Found {len(code_seq)} items. "
                            f"Per DICOM PS3.3 C.7.2.2, only a single Item shall be included in this Sequence."
                        )

                # Validate effective date/time range if present
                PatientStudyValidator._validate_effective_datetime_range(
                    item, f"Gender Identity Sequence item {item_num}", result
                )

        # Sex Parameters for Clinical Use Category Sequence (0010,0043) - Type 3
        if hasattr(data, 'SexParametersForClinicalUseCategorySequence') and data.SexParametersForClinicalUseCategorySequence:
            spcu_seq = data.SexParametersForClinicalUseCategorySequence
            for i, item in enumerate(spcu_seq):
                item_num = i + 1

                # Sex Parameters for Clinical Use Category Code Sequence (0010,0046) - Type 1 within sequence
                if not hasattr(item, 'SexParametersForClinicalUseCategoryCodeSequence'):
                    result.add_error(
                        f"Sex Parameters for Clinical Use Category Sequence item {item_num}: "
                        f"Sex Parameters for Clinical Use Category Code Sequence (0010,0046) is Type 1 - "
                        f"required within each sequence item. Per DICOM PS3.3 C.7.2.2, this field "
                        f"must contain coded category values for clinical use guidance."
                    )

                # Validate effective date/time range if present
                PatientStudyValidator._validate_effective_datetime_range(
                    item, f"Sex Parameters for Clinical Use Category Sequence item {item_num}", result
                )

        # Person Names to Use Sequence (0010,0011) - Type 3
        if hasattr(data, 'PersonNamesToUseSequence') and data.PersonNamesToUseSequence:
            names_to_use_seq = data.PersonNamesToUseSequence
            for i, item in enumerate(names_to_use_seq):
                item_num = i + 1

                # Name to Use (0010,0012) - Type 1 within sequence
                if not hasattr(item, 'NameToUse'):
                    result.add_error(
                        f"Person Names to Use Sequence item {item_num}: Name to Use (0010,0012) is "
                        f"Type 1 - required within each sequence item. Per DICOM PS3.3 C.7.2.2, "
                        f"this field must contain the name to use when addressing the person."
                    )

                # Validate effective date/time range if present
                PatientStudyValidator._validate_effective_datetime_range(
                    item, f"Person Names to Use Sequence item {item_num}", result
                )

        # Third Person Pronouns Sequence (0010,0014) - Type 3
        if hasattr(data, 'ThirdPersonPronounsSequence') and data.ThirdPersonPronounsSequence:
            pronouns_seq = data.ThirdPersonPronounsSequence
            for i, item in enumerate(pronouns_seq):
                item_num = i + 1

                # Pronoun Code Sequence (0010,0015) - Type 1 within sequence
                if not hasattr(item, 'PronounCodeSequence'):
                    result.add_error(
                        f"Third Person Pronouns Sequence item {item_num}: Pronoun Code Sequence "
                        f"(0010,0015) is Type 1 - required within each sequence item. "
                        f"Per DICOM PS3.3 C.7.2.2, this field must contain coded pronoun values."
                    )

                # Validate effective date/time range if present
                PatientStudyValidator._validate_effective_datetime_range(
                    item, f"Third Person Pronouns Sequence item {item_num}", result
                )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(PatientStudyValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(PatientStudyValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(PatientStudyValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(PatientStudyValidator.validate_sequence_structures(data))
        
        if config.validate_semantic_constraints:
            result.merge(PatientStudyValidator._validate_semantic_constraints(data, ValidationResult()))
        
        if config.validate_cross_field_dependencies:
            result.merge(PatientStudyValidator._validate_cross_field_dependencies(data, ValidationResult()))
        
        if config.validate_semantic_constraints:
            result.merge(PatientStudyValidator._validate_usage_patterns(data, ValidationResult()))
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate Type 1C and 2C conditional requirements according to PS3.3 C.7.2.2.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Delegate to the new granular method
        conditional_result = PatientStudyValidator.validate_conditional_requirements(data)
        result.merge(conditional_result)
        return result

    
    @staticmethod
    def _validate_enumerated_values(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate enumerated values against DICOM PS3.3 specifications.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Delegate to the new granular method
        enum_result = PatientStudyValidator.validate_enumerated_values(data)
        result.merge(enum_result)
        return result

    
    @staticmethod
    def _validate_sequence_requirements(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate sequence structure requirements according to DICOM PS3.3 C.7.2.2.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Delegate to the new granular method
        sequence_result = PatientStudyValidator.validate_sequence_structures(data)
        result.merge(sequence_result)
        return result

    @staticmethod
    def _validate_semantic_constraints(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate semantic constraints and logical consistency.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Validate Patient Age format if present (0010,1010)
        if 'PatientAge' in data:
            PatientStudyValidator._validate_age_format(data.PatientAge, result)

        # Validate Last Menstrual Date format if present (0010,21D0)
        if 'LastMenstrualDate' in data:
            PatientStudyValidator._validate_date_format(
                data.LastMenstrualDate,
                "Last Menstrual Date (0010,21D0)",
                result
            )

        # Validate numeric ranges for physical measurements
        PatientStudyValidator._validate_physical_measurement_ranges(data, result)
        
        return result

    @staticmethod
    def _validate_cross_field_dependencies(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate cross-field dependencies and relationships.

        Args:
            data: pydicom Dataset OR BaseModule instance
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Check pregnancy information consistency
        if 'PregnancyStatus' in data and 'PatientSex' in data:
            pregnancy_status = data.PregnancyStatus
            patient_sex = data.PatientSex  # From Patient Module
            
            if patient_sex.upper() not in ['F', 'FEMALE'] and str(pregnancy_status) != '4':
                result.add_warning(
                    f"Pregnancy Status (0010,21C0) is present for patient with sex '{patient_sex}'. "
                    f"Consider if this is appropriate or if status should be 'Unknown' (4). "
                    f"Per clinical guidelines, pregnancy status is typically relevant for female patients."
                )

        # Check medical information consistency
        if 'MedicalAlerts' in data and 'Allergies' in data:
            medical_alerts = data.MedicalAlerts
            allergies = data.Allergies
            
            # Check for potential overlap or inconsistencies
            if 'allerg' in medical_alerts.lower() and 'none' in allergies.lower():
                result.add_warning(
                    "Medical Alerts (0010,2000) mentions allergies but Allergies (0010,2110) "
                    "indicates 'none'. Consider reviewing for consistency."
                )

        # Check visit information completeness
        PatientStudyValidator._validate_visit_information_completeness(data, result)
        
        return result

    @staticmethod
    def _validate_effective_datetime_range(item: Dataset, context: str, result: ValidationResult) -> None:
        """Validate effective date/time range logic for sequence items.

        Args:
            item: Sequence item dataset to validate
            context: Context string for error messages
            result: ValidationResult to update
        """
        start_dt = item.get('EffectiveStartDateTime')
        stop_dt = item.get('EffectiveStopDateTime')

        if start_dt and stop_dt:
            # Both present - validate that start <= stop
            # Note: This is a simplified check. Full datetime parsing would be more robust
            if str(start_dt) > str(stop_dt):
                result.add_warning(
                    f"{context}: Effective Start DateTime (0040,A034) '{start_dt}' appears to be "
                    f"after Effective Stop DateTime (0040,A035) '{stop_dt}'. "
                    f"Please verify the date/time range is correct."
                )

    @staticmethod
    def _validate_age_format(age: str, result: ValidationResult) -> None:
        """Validate Patient Age format according to DICOM standard.

        Args:
            age: Age string to validate
            result: ValidationResult to update
        """
        # DICOM age format: nnnD, nnnW, nnnM, or nnnY
        age_pattern = r'^\d{3}[DWMY]$'

        if not re.match(age_pattern, age):
            result.add_error(
                f"Patient Age (0010,1010) format is invalid: '{age}'. "
                f"Must be in format nnnD (days), nnnW (weeks), nnnM (months), or nnnY (years) "
                f"where nnn is a 3-digit number. Example: '045Y' for 45 years."
            )

    @staticmethod
    def _validate_date_format(date_str: str, field_name: str, result: ValidationResult) -> None:
        """Validate DICOM date format (YYYYMMDD).

        Args:
            date_str: Date string to validate
            field_name: Field name for error messages
            result: ValidationResult to update
        """
        # DICOM date format: YYYYMMDD
        date_pattern = r'^\d{8}$'

        if not re.match(date_pattern, date_str):
            result.add_error(
                f"{field_name} format is invalid: '{date_str}'. "
                f"Must be in DICOM date format YYYYMMDD. Example: '20240315' for March 15, 2024."
            )
        else:
            # Additional validation for valid date ranges
            try:
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])

                if not (1900 <= year <= 2100):
                    result.add_warning(f"{field_name} year {year} is outside typical range (1900-2100)")
                if not (1 <= month <= 12):
                    result.add_error(f"{field_name} month {month} is invalid (must be 01-12)")
                if not (1 <= day <= 31):
                    result.add_error(f"{field_name} day {day} is invalid (must be 01-31)")
            except ValueError:
                result.add_error(f"{field_name} contains non-numeric characters: '{date_str}'")

    @staticmethod
    def _validate_physical_measurement_ranges(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate physical measurement values are within reasonable ranges.

        Args:
            data: Dataset or BaseModule to validate
            result: ValidationResult to update
        """
        # Patient Size (0010,1020) - in meters
        if 'PatientSize' in data:
            patient_size = data.PatientSize
            try:
                size_val = float(patient_size)
                if not (0.1 <= size_val <= 3.0):  # 10cm to 3m seems reasonable
                    result.add_warning(
                        f"Patient Size (0010,1020) value {size_val}m is outside typical range "
                        f"(0.1-3.0 meters). Please verify this measurement is correct."
                    )
            except (ValueError, TypeError):
                result.add_error(f"Patient Size (0010,1020) must be a numeric value in meters: '{patient_size}'")

        # Patient Weight (0010,1030) - in kilograms
        if 'PatientWeight' in data:
            patient_weight = data.PatientWeight
            try:
                weight_val = float(patient_weight)
                if not (0.5 <= weight_val <= 1000.0):  # 0.5kg to 1000kg seems reasonable
                    result.add_warning(
                        f"Patient Weight (0010,1030) value {weight_val}kg is outside typical range "
                        f"(0.5-1000.0 kg). Please verify this measurement is correct."
                    )
            except (ValueError, TypeError):
                result.add_error(f"Patient Weight (0010,1030) must be a numeric value in kilograms: '{patient_weight}'")

        # Patient Body Mass Index (0010,1022) - kg/m²
        if 'PatientBodyMassIndex' in data:
            bmi = data.PatientBodyMassIndex
            try:
                bmi_val = float(bmi)
                if not (10.0 <= bmi_val <= 100.0):  # Extreme but possible range
                    result.add_warning(
                        f"Patient Body Mass Index (0010,1022) value {bmi_val} kg/m² is outside "
                        f"typical range (10.0-100.0). Please verify this calculation is correct."
                    )
            except (ValueError, TypeError):
                result.add_error(f"Patient Body Mass Index (0010,1022) must be a numeric value: '{bmi}'")

    @staticmethod
    def _validate_visit_information_completeness(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate visit information completeness and consistency.

        Args:
            data: Dataset or BaseModule to validate
            result: ValidationResult to update
        """
        # Check if partial visit information is present
        visit_fields = [
            ('AdmissionID', '0038,0010'),
            ('ReasonForVisit', '0032,1066'),
            ('ServiceEpisodeID', '0038,0060')
        ]

        present_fields = []
        for field_name, tag in visit_fields:
            if field_name in data and data[field_name]:
                present_fields.append((field_name, tag))

        # If some visit information is present, suggest completing the set
        if 1 <= len(present_fields) < len(visit_fields):
            present_names = [f"{name} ({tag})" for name, tag in present_fields]
            result.add_warning(
                f"Partial visit information detected: {', '.join(present_names)}. "
                f"Consider adding complete visit information for better clinical context."
            )

    @staticmethod
    def _validate_usage_patterns(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> ValidationResult:
        """Validate module usage patterns and API compliance.

        Args:
            data: Dataset or BaseModule to validate
            result: ValidationResult to update
        
        Returns:
            ValidationResult: Updated validation result
        """
        # Note: The PatientStudyModule.with_optional_elements() method should only be called
        # without arguments. If arguments are provided, users should use specialized methods
        # like with_patient_demographics(), with_medical_information(), etc.
        # This validation would be triggered by checking if invalid patterns were detected,
        # but since we can't directly detect misuse from the dataset alone, we document
        # the expected usage pattern for future validation enhancements.
        
        return result


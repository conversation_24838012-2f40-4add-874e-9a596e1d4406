"""
Patient Study Module - DICOM PS3.3 C.7.2.2

The Patient Study Module contains attributes that provide information about 
the Patient at the time the Study started.
"""
from datetime import datetime, date
from typing import Optional
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.patient_study_validator import PatientStudyValidator
from ..validators import ValidationResult, ValidationError
from ..utils.dicom_formatters import format_date_value, format_enum_string


class PatientStudyModule(BaseModule):
    """Patient Study Module implementation for DICOM PS3.3 C.7.2.2.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that provide information about the Patient at the time the Study started.
    
    Usage:
        # Create with required elements (all Type 2C/3, so empty instance is valid)
        patient_study = PatientStudyModule.from_required_elements()
        
        # Add patient demographics
        patient_study.with_patient_demographics(
            patients_age="45Y",
            patients_size=1.75,
            patients_weight=70.5,
            smoking_status=SmokingStatus.NO
        )
        
        # Add medical information
        patient_study.with_medical_information(
            medical_alerts="Drug allergies: Penicillin",
            allergies="Penicillin, Shellfish"
        )
        
        # Add pregnancy information (for female patients)
        patient_study.with_pregnancy_information(
            pregnancy_status=PregnancyStatus.NOT_PREGNANT,
            last_menstrual_date="20240101"
        )
        
        # Validate
        result = patient_study.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'PatientStudyModule':
        """Create PatientStudyModule from all required (Type 1 and Type 2) data elements.
        
        Note: All elements in this module are Type 2C or Type 3, so no required elements.
        
        Returns:
            PatientStudyModule: New empty dataset instance
        """
        return cls()
    
    def with_optional_elements(self, **kwargs) -> 'PatientStudyModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Patient Study Module has Type 3 elements, but they are accessed via specialized methods.
        This method is provided for API consistency but redirects to specialized methods.
        Use with_patient_demographics(), with_medical_information(), etc. instead.
        
        Args:
            **kwargs: Optional elements (redirected to specialized methods)
            
        Returns:
            PatientStudyModule: Self for method chaining
        """
        if kwargs:
            # Note: Validation will catch invalid usage patterns when validate() is called
            pass
        return self
    
    def with_patient_demographics(
        self,
        patients_age: str | None = None,
        patients_size: float | None = None,
        patients_weight: float | None = None,
        patients_body_mass_index: float | None = None,
        measured_ap_dimension: float | None = None,
        measured_lateral_dimension: float | None = None,
        patients_size_code_sequence: list[Dataset] | None = None,
        smoking_status: str | SmokingStatus | None = None,
        occupation: str | None = None
    ) -> 'PatientStudyModule':
        """Add patient demographic information.
        
        Args:
            patients_age (str | None): Age of the Patient (0010,1010) Type 3
            patients_size (float | None): Length or size of the Patient, in meters (0010,1020) Type 3
            patients_weight (float | None): Weight of the Patient, in kilograms (0010,1030) Type 3
            patients_body_mass_index (float | None): Body Mass Index in kg/m2 (0010,1022) Type 3
            measured_ap_dimension (float | None): Thickness in mm, antero-posterior dimension (0010,1023) Type 3
            measured_lateral_dimension (float | None): Side-to-side dimension in mm (0010,1024) Type 3
            patients_size_code_sequence (list[Dataset] | None): Patient's size category codes (0010,1021) Type 3
            smoking_status (str | SmokingStatus | None): Whether Patient smokes (0010,21A0) Type 3
            occupation (str | None): Occupation of the Patient (0010,2180) Type 3
            
        Returns:
            PatientStudyModule: Self with demographic elements added
        """
        if patients_age is not None:
            self.PatientAge = patients_age
        if patients_size is not None:
            self.PatientSize = patients_size
        if patients_weight is not None:
            self.PatientWeight = patients_weight
        if patients_body_mass_index is not None:
            self.PatientBodyMassIndex = patients_body_mass_index
        if measured_ap_dimension is not None:
            self.MeasuredAPDimension = measured_ap_dimension
        if measured_lateral_dimension is not None:
            self.MeasuredLateralDimension = measured_lateral_dimension
        if patients_size_code_sequence is not None:
            self.PatientSizeCodeSequence = patients_size_code_sequence
        if smoking_status is not None:
            self.SmokingStatus = format_enum_string(smoking_status)
        if occupation is not None:
            self.Occupation = occupation
        return self
    
    def with_medical_information(
        self,
        medical_alerts: str | None = None,
        allergies: str | None = None,
        additional_patient_history: str | None = None,
        patient_state: str | None = None,
        admitting_diagnoses_description: str | None = None,
        admitting_diagnoses_code_sequence: list[Dataset] | None = None,
        principal_diagnosis_code_sequence: list[Dataset] | None = None,
        primary_diagnosis_code_sequence: list[Dataset] | None = None,
        secondary_diagnoses_code_sequence: list[Dataset] | None = None,
        histological_diagnoses_code_sequence: list[Dataset] | None = None
    ) -> 'PatientStudyModule':
        """Add medical information and diagnoses.
        
        Args:
            medical_alerts (str | None): Conditions to alert medical staff (0010,2000) Type 3
            allergies (str | None): Prior reactions to contrast agents or allergies (0010,2110) Type 3
            additional_patient_history (str | None): Additional medical history (0010,21B0) Type 3
            patient_state (str | None): Description of Patient state (0038,0500) Type 3
            admitting_diagnoses_description (str | None): Reason patient sought care (0008,1080) Type 3
            admitting_diagnoses_code_sequence (list[Dataset] | None): Coded reasons for care (0008,1084) Type 3
            principal_diagnosis_code_sequence (list[Dataset] | None): Condition chiefly responsible (0008,1301) Type 3
            primary_diagnosis_code_sequence (list[Dataset] | None): Most serious condition (0008,1302) Type 3
            secondary_diagnoses_code_sequence (list[Dataset] | None): Coexisting conditions (0008,1303) Type 3
            histological_diagnoses_code_sequence (list[Dataset] | None): Histopathology confirmed (0008,1304) Type 3
            
        Returns:
            PatientStudyModule: Self with medical information elements added
        """
        if medical_alerts is not None:
            self.MedicalAlerts = medical_alerts
        if allergies is not None:
            self.Allergies = allergies
        if additional_patient_history is not None:
            self.AdditionalPatientHistory = additional_patient_history
        if patient_state is not None:
            self.PatientState = patient_state
        if admitting_diagnoses_description is not None:
            self.AdmittingDiagnosesDescription = admitting_diagnoses_description
        if admitting_diagnoses_code_sequence is not None:
            self.AdmittingDiagnosesCodeSequence = admitting_diagnoses_code_sequence
        if principal_diagnosis_code_sequence is not None:
            self.PrincipalDiagnosisCodeSequence = principal_diagnosis_code_sequence
        if primary_diagnosis_code_sequence is not None:
            self.PrimaryDiagnosisCodeSequence = primary_diagnosis_code_sequence
        if secondary_diagnoses_code_sequence is not None:
            self.SecondaryDiagnosesCodeSequence = secondary_diagnoses_code_sequence
        if histological_diagnoses_code_sequence is not None:
            self.HistologicalDiagnosesCodeSequence = histological_diagnoses_code_sequence
        return self
    
    def with_pregnancy_information(
        self,
        pregnancy_status: int | PregnancyStatus | None = None,
        last_menstrual_date: str | datetime | date | None = None
    ) -> 'PatientStudyModule':
        """Add pregnancy-related information.
        
        Args:
            pregnancy_status (int | PregnancyStatus | None): Pregnancy state (0010,21C0) Type 3
            last_menstrual_date (str | datetime | date | None): Date of last menstrual period (0010,21D0) Type 3
            
        Returns:
            PatientStudyModule: Self with pregnancy information elements added
        """
        if pregnancy_status is not None:
            if isinstance(pregnancy_status, PregnancyStatus):
                self.PregnancyStatus = pregnancy_status.value
            else:
                self.PregnancyStatus = pregnancy_status
        if last_menstrual_date is not None:
            self.LastMenstrualDate = format_date_value(last_menstrual_date)
        return self
    
    def with_visit_information(
        self,
        admission_id: str | None = None,
        issuer_of_admission_id_sequence: list[Dataset] | None = None,
        reason_for_visit: str | None = None,
        reason_for_visit_code_sequence: list[Dataset] | None = None,
        service_episode_id: str | None = None,
        issuer_of_service_episode_id_sequence: list[Dataset] | None = None,
        service_episode_description: str | None = None
    ) -> 'PatientStudyModule':
        """Add visit and admission information.
        
        Args:
            admission_id (str | None): Identifier of the Visit (0038,0010) Type 3
            issuer_of_admission_id_sequence (list[Dataset] | None): Assigning Authority for admission ID (0038,0014) Type 3
            reason_for_visit (str | None): Reason for this visit (0032,1066) Type 3
            reason_for_visit_code_sequence (list[Dataset] | None): Coded reason for visit (0032,1067) Type 3
            service_episode_id (str | None): Identifier of the Service Episode (0038,0060) Type 3
            issuer_of_service_episode_id_sequence (list[Dataset] | None): Assigning Authority for episode ID (0038,0064) Type 3
            service_episode_description (str | None): Description of service episode type (0038,0062) Type 3
            
        Returns:
            PatientStudyModule: Self with visit information elements added
        """
        if admission_id is not None:
            self.AdmissionID = admission_id
        if issuer_of_admission_id_sequence is not None:
            self.IssuerOfAdmissionIDSequence = issuer_of_admission_id_sequence
        if reason_for_visit is not None:
            self.ReasonForVisit = reason_for_visit
        if reason_for_visit_code_sequence is not None:
            self.ReasonForVisitCodeSequence = reason_for_visit_code_sequence
        if service_episode_id is not None:
            self.ServiceEpisodeID = service_episode_id
        if issuer_of_service_episode_id_sequence is not None:
            self.IssuerOfServiceEpisodeIDSequence = issuer_of_service_episode_id_sequence
        if service_episode_description is not None:
            self.ServiceEpisodeDescription = service_episode_description
        return self

    def with_non_human_organism_info(
        self,
        patients_sex_neutered: str | PatientSexNeutered
    ) -> 'PatientStudyModule':
        """Add non-human organism information with required conditional logic.

        Note: Patient's Sex Neutered (0010,2203) is Type 2C - required if Patient is a non-human organism.

        Args:
            patients_sex_neutered (str | PatientSexNeutered): Whether procedure performed to render sterile (0010,2203) Type 2C

        Returns:
            PatientStudyModule: Self with non-human organism elements added
        """
        self.PatientSexNeutered = format_enum_string(patients_sex_neutered)
        return self

    def with_gender_identity(
        self,
        gender_identity_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add gender identity information.

        Args:
            gender_identity_sequence (list[Dataset]): Individual's personal sense of gender (0010,0041) Type 3

        Returns:
            PatientStudyModule: Self with gender identity elements added
        """
        self.GenderIdentitySequence = gender_identity_sequence
        return self

    def with_sex_parameters_for_clinical_use(
        self,
        sex_parameters_for_clinical_use_category_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add sex parameters for clinical use.

        Args:
            sex_parameters_for_clinical_use_category_sequence (list[Dataset]): Clinical use guidance (0010,0043) Type 3

        Returns:
            PatientStudyModule: Self with sex parameters elements added
        """
        self.SexParametersForClinicalUseCategorySequence = sex_parameters_for_clinical_use_category_sequence
        return self

    def with_person_names_to_use(
        self,
        person_names_to_use_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add preferred names for addressing the person.

        Args:
            person_names_to_use_sequence (list[Dataset]): Names to use when addressing person (0010,0011) Type 3

        Returns:
            PatientStudyModule: Self with person names elements added
        """
        self.PersonNamesToUseSequence = person_names_to_use_sequence
        return self

    def with_third_person_pronouns(
        self,
        third_person_pronouns_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add third person pronouns information.

        Args:
            third_person_pronouns_sequence (list[Dataset]): Pronouns to use in reference (0010,0014) Type 3

        Returns:
            PatientStudyModule: Self with pronouns elements added
        """
        self.ThirdPersonPronounsSequence = third_person_pronouns_sequence
        return self

    @staticmethod
    def create_gender_identity_item(
        gender_identity_code_sequence: list[Dataset],
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        gender_identity_comment: Optional[str] = None
    ) -> Dataset:
        """Create an item for Gender Identity Sequence (0010,0041).

        Args:
            gender_identity_code_sequence (list[Dataset]): Coded gender identity (0010,0044) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            gender_identity_comment (str | None): Comments on gender identity (0010,0045) Type 3

        Returns:
            Dataset: Sequence item with gender identity information
        """
        item = Dataset()
        item.GenderIdentityCodeSequence = gender_identity_code_sequence
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if gender_identity_comment is not None:
            item.GenderIdentityComment = gender_identity_comment
        return item

    @staticmethod
    def create_gender_identity_code(
        code_meaning: str,
        code_value: str = "",
        long_code_value: str = "",
        coding_scheme_designator: str = "",
        coding_scheme_version: str = "",
        urn_code_value: str = ""
    ) -> Dataset:
        """Create a gender identity code item for Gender Identity Sequence (0010,0041).

        Args:
            code_meaning (str): Meaning of the code (0008,0104) Type 1
            code_value (str): Value of the code (0008,0100) Type 1C
            long_code_value (str): Long value of the code (0008,0119) Type 1C
            coding_scheme_designator (str): Designator of the coding scheme (0008,0102) Type 1C
            coding_scheme_version (str): Version of the coding scheme (0008,0103) Type 1C
            urn_code_value (str): URN value of the code (0008,0120) Type 1C

        Returns:
            Dataset: Sequence item with gender identity code information
        """
        item = Dataset()
        item.CodeMeaning = code_meaning
        item.CodeValue = code_value
        item.LongCodeValue = long_code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodingSchemeVersion = coding_scheme_version
        item.UrnCodeValue = urn_code_value
        return item

    @staticmethod
    def create_person_name_to_use_item(
        name_to_use: str,
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        name_to_use_comment: Optional[str] = None
    ) -> Dataset:
        """Create an item for Person Names to Use Sequence (0010,0011).

        Args:
            name_to_use (str): Name to use when addressing person (0010,0012) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            name_to_use_comment (str | None): Explanation of name usage (0010,0013) Type 3

        Returns:
            Dataset: Sequence item with name information
        """
        item = Dataset()
        item.NameToUse = name_to_use
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if name_to_use_comment is not None:
            item.NameToUseComment = name_to_use_comment
        return item

    @staticmethod
    def create_sex_parameters_for_clinical_use_item(
        sex_parameters_for_clinical_use_category_code_sequence: list[Dataset],
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        sex_parameters_for_clinical_use_category_comment: Optional[str] = None,
        sex_parameters_for_clinical_use_category_reference: Optional[str] = None
    ) -> Dataset:
        """Create an item for Sex Parameters for Clinical Use Category Sequence (0010,0043).

        Args:
            sex_parameters_for_clinical_use_category_code_sequence (list[Dataset]): Category code (0010,0046) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            sex_parameters_for_clinical_use_category_comment (str | None): Comment (0010,0042) Type 2C
            sex_parameters_for_clinical_use_category_reference (str | None): Reference (0010,0047) Type 2C

        Returns:
            Dataset: Sequence item with sex parameters information
        """
        item = Dataset()
        item.SexParametersForClinicalUseCategoryCodeSequence = sex_parameters_for_clinical_use_category_code_sequence
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if sex_parameters_for_clinical_use_category_comment is not None:
            item.SexParametersForClinicalUseCategoryComment = sex_parameters_for_clinical_use_category_comment
        if sex_parameters_for_clinical_use_category_reference is not None:
            item.SexParametersForClinicalUseCategoryReference = sex_parameters_for_clinical_use_category_reference
        return item

    @staticmethod
    def create_third_person_pronouns_item(
        pronoun_code_sequence: list[Dataset],
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        pronoun_comment: Optional[str] = None
    ) -> Dataset:
        """Create an item for Third Person Pronouns Sequence (0010,0014).

        Args:
            pronoun_code_sequence (list[Dataset]): Pronoun code (0010,0015) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            pronoun_comment (str | None): Pronoun comment (0010,0016) Type 3

        Returns:
            Dataset: Sequence item with pronoun information
        """
        item = Dataset()
        item.PronounCodeSequence = pronoun_code_sequence
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if pronoun_comment is not None:
            item.PronounComment = pronoun_comment
        return item

    @property
    def has_demographic_info(self) -> bool:
        """Check if demographic information is present.

        Returns:
            bool: True if any demographic elements are present
        """
        return any(attr in self for attr in [
            'PatientAge', 'PatientSize', 'PatientWeight', 'PatientBodyMassIndex'
        ])

    @property
    def has_medical_info(self) -> bool:
        """Check if medical information is present.

        Returns:
            bool: True if any medical elements are present
        """
        return any(attr in self for attr in [
            'MedicalAlerts', 'Allergies', 'AdditionalPatientHistory'
        ])

    @property
    def has_pregnancy_info(self) -> bool:
        """Check if pregnancy information is present.

        Returns:
            bool: True if pregnancy-related elements are present
        """
        return ('PregnancyStatus' in self or 'LastMenstrualDate' in self)

    @property
    def has_visit_info(self) -> bool:
        """Check if visit information is present.

        Returns:
            bool: True if visit-related elements are present
        """
        return any(attr in self for attr in [
            'AdmissionID', 'ReasonForVisit', 'ServiceEpisodeID'
        ])

    @property
    def is_non_human_organism(self) -> bool:
        """Check if this represents a non-human organism.

        Returns:
            bool: True if sex neutered information indicates non-human organism
        """
        return 'PatientSexNeutered' in self

    @property
    def has_gender_identity_info(self) -> bool:
        """Check if gender identity information is present.

        Returns:
            bool: True if gender identity sequence is present
        """
        return 'GenderIdentitySequence' in self

    @property
    def has_sex_parameters_for_clinical_use(self) -> bool:
        """Check if sex parameters for clinical use information is present.

        Returns:
            bool: True if sex parameters sequence is present
        """
        return 'SexParametersForClinicalUseCategorySequence' in self

    @property
    def has_person_names_to_use(self) -> bool:
        """Check if person names to use information is present.

        Returns:
            bool: True if person names sequence is present
        """
        return 'PersonNamesToUseSequence' in self

    @property
    def has_third_person_pronouns(self) -> bool:
        """Check if third person pronouns information is present.

        Returns:
            bool: True if pronouns sequence is present
        """
        return 'ThirdPersonPronounsSequence' in self

    # Public convenience methods for specific validation checks with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return PatientStudyValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return PatientStudyValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return PatientStudyValidator.validate_enumerated_values(self)
    
    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return PatientStudyValidator.validate_sequence_structures(self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return PatientStudyValidator.validate(self, config)  # Direct self reference for performance
    
    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.
        
        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            PatientStudyValidator.validate_required_elements,
            "Required elements validation failed"
        )
    
    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            PatientStudyValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            PatientStudyValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
    
    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            PatientStudyValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )

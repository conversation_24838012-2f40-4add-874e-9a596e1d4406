"""
Overlay Plane Module Implementation - DICOM PS3.3 C.9.2

This module implements the DICOM Overlay Plane Module as specified in PS3.3 Section C.9.2.
The Overlay Plane Module describes characteristics of graphics or bit-mapped text overlays
that are spatially associated with medical images for annotation, measurement, or region
of interest (ROI) definition purposes.

Overlay planes provide a mechanism to display graphical annotations, text labels, or
ROI boundaries over medical images without modifying the underlying pixel data. This is
essential for clinical workflows involving image annotation, measurement marking, or
treatment planning visualization.

Common use cases include:
- ROI boundary marking for radiation therapy planning
- Anatomical structure annotations in diagnostic imaging
- Measurement tool overlays for distance/area calculations
- Text labels for image identification and orientation
- Graphics overlays for surgical planning visualization

The module supports both Graphics (G) and ROI (R) overlay types, with optional statistical
parameters for ROI overlays including area, mean, and standard deviation calculations.
Multiple overlay planes can be defined using different group numbers (6000-601E).

BREAKING CHANGES: This module now uses composition-based architecture with internal
dataset management instead of inheriting from pydicom.Dataset.
"""
from .base_module import BaseModule
from ..enums.image_enums import OverlayType, OverlaySubtype
from ..validators.modules.overlay_plane_validator import OverlayPlaneValidator
from ..validators.modules.base_validator import ValidationConfig
from ..validators import ValidationResult
from ..validators.validation_error import ValidationError
from ..utils.dicom_formatters import format_enum_string


class OverlayPlaneModule(BaseModule):
    """DICOM Overlay Plane Module implementation conforming to PS3.3 Section C.9.2.
    
    This class provides a user-friendly interface for creating and managing DICOM overlay
    planes, which are essential for displaying graphics, text annotations, and region of
    interest (ROI) boundaries over medical images without altering the underlying pixel data.
    
    Overlay planes are commonly used in:
    - Radiation therapy planning: ROI boundary definition and dose distribution visualization
    - Diagnostic imaging: Anatomical structure annotation and measurement overlays
    - Surgical planning: Critical structure marking and approach visualization
    - Image analysis: Automated segmentation result display and manual annotation
    
    The module supports both Graphics (G) and ROI (R) overlay types:
    - Graphics overlays: General purpose graphics, text, and annotation display
    - ROI overlays: Specific region definitions with optional statistical calculations
    
    Technical Implementation Notes:
    - Uses DICOM group 6000 by default (configurable to any even group 6000-601E)
    - Supports 1-bit overlay data with automatic size calculation and validation
    - Provides spatial coordinate validation against associated image dimensions
    - Implements proper DICOM tag assignment for repeating overlay groups
    
    DICOM Compliance:
    This implementation follows DICOM PS3.3 C.9.2 specifications exactly, ensuring
    compatibility with all DICOM-compliant viewers and radiation therapy systems.
    
    Args:
        None (use factory methods for creation)
    
    Example:
        Basic overlay creation for ROI marking:
        
        >>> # Create ROI overlay for tumor boundary
        >>> overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512, 
        ...     overlay_type=OverlayType.ROI,
        ...     overlay_origin=[100, 100],  # Start at image position (100,100)
        ...     overlay_data=roi_binary_data  # 1-bit ROI mask data
        ... )
        
        >>> # Add descriptive information
        >>> overlay.with_optional_elements(
        ...     overlay_description="Primary tumor boundary for treatment planning",
        ...     overlay_subtype=OverlaySubtype.USER,
        ...     overlay_label="GTV"  # Gross Tumor Volume
        ... )
        
        >>> # Add ROI statistics for treatment planning
        >>> overlay.with_roi_statistics(
        ...     roi_area=2048,  # ROI area in pixels
        ...     roi_mean=2850.5,  # Mean HU value within ROI
        ...     roi_standard_deviation=125.3  # HU standard deviation
        ... )
        
        >>> # Validate DICOM compliance
        >>> result = overlay.validate()
        >>> if result.has_errors:
        ...     print("Validation errors:", result.errors)
        
        Graphics overlay for measurement display:
        
        >>> # Create graphics overlay for distance measurements
        >>> measurement_overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512,
        ...     overlay_type=OverlayType.GRAPHICS,
        ...     overlay_origin=[1, 1],  # Cover entire image
        ...     overlay_data=measurement_graphics_data
        ... )
        
        >>> # Add measurement description
        >>> measurement_overlay.with_optional_elements(
        ...     overlay_description="Linear measurements for dose verification",
        ...     overlay_label="Measurements"
        ... )
        
        Multiple overlay groups for complex annotations:
        
        >>> # Create second overlay using different group
        >>> annotation_overlay = OverlayPlaneModule.from_required_elements(
        ...     overlay_rows=512,
        ...     overlay_columns=512,
        ...     overlay_type=OverlayType.GRAPHICS,
        ...     overlay_origin=[1, 1],
        ...     overlay_data=annotation_data,
        ...     overlay_group=0x6002  # Use group 6002 instead of default 6000
        ... )
    
    See Also:
        - OverlayType enum: Defines valid overlay type values (GRAPHICS, ROI)
        - OverlaySubtype enum: Defines overlay purpose classifications
        - OverlayPlaneValidator: Validates overlay data for DICOM compliance
        - DICOM PS3.3 Section C.9.2: Complete specification reference
    """

    @classmethod
    def from_required_elements(
        cls,
        overlay_rows: int,
        overlay_columns: int,
        overlay_type: str | OverlayType,
        overlay_origin: list[int],
        overlay_data: bytes,
        overlay_group: int = 0x6000
    ) -> 'OverlayPlaneModule':
        """Create OverlayPlaneModule with all required DICOM elements for overlay display.

        Creates a complete overlay plane module with all Type 1 (required) DICOM attributes.
        The overlay data represents a 1-bit binary mask that defines overlay graphics or ROI
        boundaries to be displayed over the associated medical image.

        Args:
            overlay_rows (int): Number of pixel rows in the overlay plane (60xx,0010) Type 1.
                Must match or be smaller than the associated image height.

            overlay_columns (int): Number of pixel columns in the overlay plane (60xx,0011) Type 1.
                Must match or be smaller than the associated image width.

            overlay_type (str | OverlayType): Defines overlay purpose and interpretation (60xx,0040) Type 1.
                OverlayType.GRAPHICS ("G") for general graphics, OverlayType.ROI ("R") for regions.

            overlay_origin (list[int]): Starting position [row, column] of overlay (60xx,0050) Type 1.
                Uses 1-based DICOM coordinates relative to the image pixel matrix.

            overlay_data (bytes): Binary overlay pixel data (60xx,3000) Type 1.
                1-bit per pixel data arranged left-to-right, top-to-bottom.

            overlay_group (int, optional): DICOM overlay group number (default: 0x6000).
                Must be even number between 0x6000 and 0x601E for multiple overlay planes.

        Returns:
            OverlayPlaneModule: Configured module ready for optional elements and validation.
            
        Note:
            Overlay group validation is performed by the OverlayPlaneValidator during validation.
        """
        instance = cls()

        # Store overlay group for tag calculation
        instance._overlay_group = overlay_group

        # Set overlay elements using proper DICOM tags for repeating groups
        instance.set((overlay_group, 0x0010), overlay_rows, "US")  # Overlay Rows
        instance.set((overlay_group, 0x0011), overlay_columns, "US")  # Overlay Columns
        instance.set((overlay_group, 0x0040), format_enum_string(overlay_type), "CS")  # Overlay Type
        instance.set((overlay_group, 0x0050), overlay_origin, "SS")  # Overlay Origin
        instance.set((overlay_group, 0x0100), 1, "US")  # Overlay Bits Allocated (always 1)
        instance.set((overlay_group, 0x0102), 0, "US")  # Overlay Bit Position (always 0)
        instance.set((overlay_group, 0x3000), overlay_data, "OW")  # Overlay Data

        return instance
    
    def with_optional_elements(
        self,
        overlay_description: str | None = None,
        overlay_subtype: str | OverlaySubtype | None = None,
        overlay_label: str | None = None
    ) -> 'OverlayPlaneModule':
        """Add optional descriptive elements to enhance overlay identification and purpose.

        These Type 3 elements provide human-readable information about the overlay's content
        and purpose, improving clinical workflow by allowing users to quickly identify and
        understand different overlays within the same image dataset.

        Args:
            overlay_description (str | None): Detailed description of overlay content (60xx,0022) Type 3.
                Free-text field for comprehensive overlay documentation.

            overlay_subtype (str | OverlaySubtype | None): Standardized overlay purpose (60xx,0045) Type 3.
                OverlaySubtype.USER for user-defined annotations, OverlaySubtype.AUTOMATED
                for computer-generated overlays, OverlaySubtype.ACTIVE_IMAGE_AREA for active area identification.

            overlay_label (str | None): Short identifying text label (60xx,1500) Type 3.
                Concise label for quick overlay identification in viewer interfaces.

        Returns:
            OverlayPlaneModule: Self-reference enabling method chaining for fluent interface.
        """
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if overlay_description is not None:
            self.set((overlay_group, 0x0022), overlay_description, "LO")  # Overlay Description

        if overlay_subtype is not None:
            self.set((overlay_group, 0x0045), format_enum_string(overlay_subtype), "CS")  # Overlay Subtype

        if overlay_label is not None:
            self.set((overlay_group, 0x1500), overlay_label, "LO")  # Overlay Label
        return self
    
    def with_roi_statistics(
        self,
        roi_area: int | None = None,
        roi_mean: float | None = None,
        roi_standard_deviation: float | None = None
    ) -> 'OverlayPlaneModule':
        """Add statistical analysis parameters for ROI overlays used in quantitative imaging.

        These Type 3 elements provide quantitative measurements within the region of interest
        defined by the overlay, essential for treatment planning dose calculations, diagnostic
        analysis, and research applications.

        Note: ROI statistics are only meaningful when overlay_type is OverlayType.ROI ("R").
        Graphics overlays typically do not require statistical analysis.

        Args:
            roi_area (int | None): Total number of pixels within ROI boundary (60xx,1301) Type 3.
                Represents the spatial extent of the region in pixels.

            roi_mean (float | None): Average pixel value within ROI region (60xx,1302) Type 3.
                For CT images, represents mean Hounsfield Unit (HU) value indicating tissue density.

            roi_standard_deviation (float | None): Pixel value standard deviation (60xx,1303) Type 3.
                Measures intensity variation within the ROI, indicating tissue homogeneity.

        Returns:
            OverlayPlaneModule: Self-reference enabling method chaining for fluent interface.
        """
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if roi_area is not None:
            self.set((overlay_group, 0x1301), str(roi_area), "IS")  # ROI Area

        if roi_mean is not None:
            self.set((overlay_group, 0x1302), str(roi_mean), "DS")  # ROI Mean

        if roi_standard_deviation is not None:
            self.set((overlay_group, 0x1303), str(roi_standard_deviation), "DS")  # ROI Standard Deviation
        return self

    def with_multi_frame_overlay(
        self,
        number_of_frames_in_overlay: int,
        image_frame_origin: list[int]
    ) -> 'OverlayPlaneModule':
        """Add multi-frame overlay elements for frame-specific overlay display.

        These Type 3 elements define overlay behavior in multi-frame images. When present,
        the overlay applies only to specific frames. When absent, the overlay applies to
        all frames in the multi-frame image (per DICOM PS3.3 C.9.2.1.4).

        Args:
            number_of_frames_in_overlay (int): Number of frames in overlay (60xx,0015) Type 3.
                Defines how many frames contain overlay data.

            image_frame_origin (list[int]): Frame numbers where overlay starts (60xx,0051) Type 3.
                1-based frame numbers indicating which frames contain overlay data.

        Returns:
            OverlayPlaneModule: Self-reference enabling method chaining for fluent interface.

        Note:
            When these elements are absent, the overlay SHALL be applied to all frames
            in the multi-frame image according to DICOM PS3.3 C.9.2.1.4.
        """
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        self.set((overlay_group, 0x0015), str(number_of_frames_in_overlay), "IS")  # Number of Frames in Overlay
        self.set((overlay_group, 0x0051), [str(frame) for frame in image_frame_origin], "IS")  # Image Frame Origin

        return self

    def _get_overlay_tag(self, element_tag: int):
        """Get overlay tag for the current overlay group.
        
        Args:
            element_tag: Element tag (e.g., 0x0010 for Overlay Rows)
            
        Returns:
            Tuple representing the full DICOM tag
        """
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        return (overlay_group, element_tag)
    
    def _has_overlay_element(self, element_tag: int) -> bool:
        """Check if overlay element exists.
        
        Args:
            element_tag: Element tag (e.g., 0x0010 for Overlay Rows)
            
        Returns:
            bool: True if element exists
        """
        return self._get_overlay_tag(element_tag) in self

    @staticmethod
    def calculate_overlay_data_size(rows: int, columns: int) -> int:
        """Calculate required overlay data size for 1-bit DICOM overlay pixel data.
        
        Computes the exact byte size needed for overlay data storage according to DICOM PS3.3
        specifications. Accounts for 1-bit per pixel encoding, byte boundary alignment, and
        DICOM even-length requirement.
        
        Args:
            rows (int): Number of pixel rows in the overlay plane.
                Must be positive integer representing vertical overlay extent.
                
            columns (int): Number of pixel columns in the overlay plane.
                Must be positive integer representing horizontal overlay extent.
            
        Returns:
            int: Required data size in bytes with proper DICOM padding.
        """
        total_bits = rows * columns
        total_bytes = (total_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length
        return total_bytes + (total_bytes % 2)
    
    @property
    def is_graphics_overlay(self) -> bool:
        """Check if this is a graphics overlay.

        Returns:
            bool: True if overlay type is GRAPHICS ("G")
        """
        if not self._has_overlay_element(0x0040):  # Overlay Type
            return False
        overlay_type_tag = self._get_overlay_tag(0x0040)
        return self[overlay_type_tag].value == "G"

    @property
    def is_roi_overlay(self) -> bool:
        """Check if this is an ROI overlay.

        Returns:
            bool: True if overlay type is ROI ("R")
        """
        if not self._has_overlay_element(0x0040):  # Overlay Type
            return False
        overlay_type_tag = self._get_overlay_tag(0x0040)
        return self[overlay_type_tag].value == "R"

    @property
    def has_roi_statistics(self) -> bool:
        """Check if ROI statistics are present.

        Returns:
            bool: True if any ROI statistical elements are present
        """
        return (self._has_overlay_element(0x1301) or  # ROI Area
                self._has_overlay_element(0x1302) or  # ROI Mean
                self._has_overlay_element(0x1303))    # ROI Standard Deviation

    @property
    def is_multi_frame_overlay(self) -> bool:
        """Check if this is a multi-frame overlay.

        Returns:
            bool: True if Number of Frames in Overlay or Image Frame Origin are present
        """
        return (self._has_overlay_element(0x0015) or  # Number of Frames in Overlay
                self._has_overlay_element(0x0051))    # Image Frame Origin

    @property
    def applies_to_all_frames(self) -> bool:
        """Check if overlay applies to all frames in multi-frame image.

        Returns:
            bool: True if overlay should be applied to all frames (when multi-frame
                  elements are absent per DICOM PS3.3 C.9.2.1.4)
        """
        return not self.is_multi_frame_overlay

    @property
    def overlay_pixel_count(self) -> int | None:
        """Calculate total number of overlay pixels.

        Returns:
            int | None: Total pixel count (rows × columns), or None if not determinable
        """
        if not (self._has_overlay_element(0x0010) and self._has_overlay_element(0x0011)):
            return None
        rows_tag = self._get_overlay_tag(0x0010)
        cols_tag = self._get_overlay_tag(0x0011)
        return int(self[rows_tag].value) * int(self[cols_tag].value)

    @property
    def expected_data_size(self) -> int | None:
        """Calculate expected overlay data size.

        Returns:
            int | None: Expected data size in bytes, or None if not determinable
        """
        if not (self._has_overlay_element(0x0010) and self._has_overlay_element(0x0011)):
            return None
        rows_tag = self._get_overlay_tag(0x0010)
        cols_tag = self._get_overlay_tag(0x0011)
        return self.calculate_overlay_data_size(int(self[rows_tag].value), int(self[cols_tag].value))

    @property
    def actual_data_size(self) -> int | None:
        """Get actual overlay data size.

        Returns:
            int | None: Actual data size in bytes, or None if not available
        """
        if not self._has_overlay_element(0x3000):  # Overlay Data
            return None
        data_tag = self._get_overlay_tag(0x3000)
        data = self[data_tag].value
        return len(data) if hasattr(data, '__len__') else None
    
    def get_overlay_origin_coordinates(self) -> tuple[int, int] | None:
        """Get overlay origin as (row, column) tuple.

        Returns:
            tuple[int, int] | None: (row, column) coordinates or None if not available
        """
        if not self._has_overlay_element(0x0050):  # Overlay Origin
            return None

        origin_tag = self._get_overlay_tag(0x0050)
        origin = self[origin_tag].value
        if len(origin) != 2:
            return None
        return (origin[0], origin[1])

    def is_overlay_within_image(self, image_rows: int, image_columns: int) -> bool:
        """Check if overlay fits within image boundaries.

        Args:
            image_rows (int): Number of rows in associated image
            image_columns (int): Number of columns in associated image

        Returns:
            bool: True if overlay fits within image boundaries
        """
        origin = self.get_overlay_origin_coordinates()
        if (origin is None or 
            not self._has_overlay_element(0x0010) or  # Overlay Rows
            not self._has_overlay_element(0x0011)):   # Overlay Columns
            return False

        origin_row, origin_col = origin
        rows_tag = self._get_overlay_tag(0x0010)
        cols_tag = self._get_overlay_tag(0x0011)
        overlay_rows = self[rows_tag].value
        overlay_cols = self[cols_tag].value

        # Calculate overlay boundaries
        overlay_end_row = origin_row + overlay_rows - 1
        overlay_end_col = origin_col + overlay_cols - 1

        # Check if overlay extends beyond image
        return (origin_row >= 1 and origin_col >= 1 and
                overlay_end_row <= image_rows and overlay_end_col <= image_columns)
    
    # Public convenience methods for specific validation checks with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return OverlayPlaneValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return OverlayPlaneValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return OverlayPlaneValidator.validate_enumerated_values(self)
    
    def check_sequence_requirements(self) -> ValidationResult:
        """Check sequence structure requirements with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return OverlayPlaneValidator.validate_sequence_structures(self)

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return OverlayPlaneValidator.validate(self, config)  # Direct self reference for performance
    
    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.
        
        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            OverlayPlaneValidator.validate_required_elements,
            "Required elements validation failed"
        )
    
    def _ensure_conditional_requirements_valid(self) -> None:
        """Ensure conditional requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If conditional requirements are invalid
        """
        self._validate_and_raise(
            OverlayPlaneValidator.validate_conditional_requirements,
            "Conditional requirements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            OverlayPlaneValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
    
    def _ensure_sequence_requirements_valid(self) -> None:
        """Ensure sequence requirements are valid, raise exception if not.
        
        Raises:
            ValidationError: If sequence requirements are invalid
        """
        self._validate_and_raise(
            OverlayPlaneValidator.validate_sequence_structures,
            "Sequence structure validation failed"
        )
